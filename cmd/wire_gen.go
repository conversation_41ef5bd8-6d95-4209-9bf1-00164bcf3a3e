// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package cmd

import (
	"github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/moego-svc-fulfillment/config"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/consumer"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/handler"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/customer"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/offering"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/order"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/organization"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service"
	"github.com/MoeGolibrary/moego-svc-fulfillment/resource"
	"google.golang.org/protobuf/proto"
)

// Injectors from wire.go:

func Init() *Handlers {
	db := resource.GetDB()
	groupClassDetailRepository := fulfillment.NewGroupClassDetailRepository(db)
	repository := fulfillment.NewFulfillmentRepository(db)
	staffTimeSlotRepository := fulfillment.NewStaffTimeSlotRepository(db)
	groupClassRepository := offering.NewGroupClassRepository()
	groupClassAttendanceRepository := fulfillment.NewGroupClassAttendanceRepository(db)
	staffTimeSlotService := service.NewStaffTimeSlotService(db, staffTimeSlotRepository, repository)
	groupClassService := service.NewGroupClassService(db, groupClassDetailRepository, repository, staffTimeSlotRepository, groupClassRepository, groupClassAttendanceRepository, staffTimeSlotService)
	petRepository := customer.NewPetRepository()
	customerRepository := customer.NewRepository()
	orderRepository := order.NewOrderRepository()
	serviceRepository := offering.NewServiceRepository()
	taxRepository := organization.NewTaxRepository()
	config := eventBusConfigProvider()
	producer := eventBusProducerProvider(config)
	fulfillmentService := service.NewFulfillmentService(db, groupClassService, repository, groupClassDetailRepository, staffTimeSlotRepository, groupClassAttendanceRepository, petRepository, customerRepository, orderRepository, serviceRepository, taxRepository, groupClassRepository, producer)
	fulfillmentHandler := handler.NewFulfillmentHandler(fulfillmentService)
	groupClassDetailHandler := handler.NewGroupClassDetailHandler(groupClassService)
	groupClassAttendanceHandler := handler.NewGroupClassAttendanceHandler(groupClassService)
	eventDispatcher := consumer.NewEventDispatcher()
	orderHandler := consumer.NewOrderHandler(fulfillmentService)
	offeringHandler := consumer.NewOfferingHandler(groupClassService)
	consumerConsumer := consumer.NewConsumer(eventDispatcher, orderHandler, offeringHandler)
	handlers := &Handlers{
		FulfillmentHandler:          fulfillmentHandler,
		GroupClassDetailHandler:     groupClassDetailHandler,
		GroupClassAttendanceHandler: groupClassAttendanceHandler,
		Consumer:                    consumerConsumer,
	}
	return handlers
}

// wire.go:

type Handlers struct {
	FulfillmentHandler          *handler.FulfillmentHandler
	GroupClassDetailHandler     *handler.GroupClassDetailHandler
	GroupClassAttendanceHandler *handler.GroupClassAttendanceHandler
	Consumer                    *consumer.Consumer
}

func eventBusConfigProvider() *eventbus.Config {
	return config.GetConfig().EventBus
}

func eventBusProducerProvider(cfg *eventbus.Config) eventbus.Producer[proto.Message] {
	producer, err := eventbus.NewProducer(cfg)
	if err != nil {
		panic(err)
	}
	return *producer
}
