plugins {
    id 'java'
    id 'jacoco'
    id 'io.spring.dependency-management' version "${springDependencyManagementVersion}"
    id 'org.springframework.boot' version "${springBootVersion}"
    id 'com.diffplug.spotless' version "${spotlessVersion}"
    id "com.github.spotbugs" version "${spotbugsVersion}"
    id "com.qqviaja.gradle.MybatisGenerator" version "${mybatisGeneratorGradlePlugin}"
}

repositories {
    mavenLocal()
    mavenCentral()
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

dependencies {
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign")
    implementation("io.awspring.cloud:spring-cloud-aws-starter-secrets-manager:3.2.1")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisBootStarterVersion}")
    implementation("org.mybatis.dynamic-sql:mybatis-dynamic-sql:${mybatisDynamicSqlVersion}")
    runtimeOnly("org.postgresql:postgresql")

    // Google Reserve merchant status query
    // https://developers.google.com/actions-center/verticals/appointments/redirect/partner-portal/inventory/merchant-status-query
    implementation("com.google.auth:google-auth-library-oauth2-http:${googleAuthLibraryOauth2HttpVersion}")

    // ssh, scp and sftp for Java, see https://github.com/hierynomus/sshj
    implementation("com.hierynomus:sshj:${sshjVersion}")

    compileOnly("org.projectlombok:lombok")
    annotationProcessor("org.projectlombok:lombok")
    implementation("org.mapstruct:mapstruct:${mapstructVerson}")
    annotationProcessor("org.mapstruct:mapstruct-processor:${mapstructVerson}")

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    testImplementation("org.springframework.boot:spring-boot-starter-test")
}

compileJava {
    options.encoding = 'UTF-8'
    options.compilerArgs << '-parameters'
}

bootJar {
    archivesBaseName = 'moego-server'
}

test {
    useJUnitPlatform()
    testLogging {
        events 'failed'
        exceptionFormat 'full'
    }
}

spotless {
    encoding 'UTF-8'
    java {
        toggleOffOn()
        removeUnusedImports()
        trimTrailingWhitespace()
        endWithNewline()
        palantirJavaFormat()

        targetExclude(
                "src/main/java/**/mapper/*",
                "src/main/java/**/entity/*"
        )

        custom('Refuse wildcard imports', {
            if (it =~ /\nimport .*\*;/) {
                throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
            }
        } as Closure<String>)
    }
}

spotbugs {
    spotbugsTest.enabled = false
    omitVisitors.addAll('FindReturnRef', 'MethodReturnCheck')
    excludeFilter.set(file("${rootDir}/config/spotbugs/excludeFilter.xml"))
}

configurations {
    mybatisGenerator
}

mybatisGenerator {
    verbose = true
    configFile = "${rootDir}/MyBatisGeneratorConfig.xml"

    dependencies {
        mybatisGenerator "org.mybatis.generator:mybatis-generator-core:${mybatisGeneratorCoreVersion}"
        mybatisGenerator "org.postgresql:postgresql"
    }
}
