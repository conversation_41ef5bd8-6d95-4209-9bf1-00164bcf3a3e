package com.moego.server.customer.service;

import com.google.common.collect.Lists;
import com.moego.common.constant.CommonConstant;
import com.moego.common.distributed.LockManager;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.dto.PageDTO;
import com.moego.common.dto.clients.BusinessClientsDTO;
import com.moego.common.dto.clients.BusinessDateClientsDTO;
import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.common.dto.clients.FilterDTO;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.PropertyEnum;
import com.moego.common.enums.PropertyTableEnum;
import com.moego.common.enums.PropertyTypeEnum;
import com.moego.common.enums.filter.OperatorEnum;
import com.moego.common.enums.filter.TypeEnum;
import com.moego.common.params.FilterParams;
import com.moego.common.params.PageQuery;
import com.moego.common.params.QueryParams;
import com.moego.common.params.SortParams;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.FilterUtils;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.account.v1.AccountServiceGrpc.AccountServiceBlockingStub;
import com.moego.idl.service.account.v1.BatchGetAccountRequest;
import com.moego.idl.service.online_booking.v1.CustomerAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.ListBlockedCustomerRequest;
import com.moego.idl.service.online_booking.v1.ListBlockedCustomerResponse;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerMetrics;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.api.IBusinessServiceAreaService;
import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import com.moego.server.customer.dto.CustomerFilterResult;
import com.moego.server.customer.dto.PhoneNumberEmailDto;
import com.moego.server.customer.mapper.MoeBusinessCustomerMapper;
import com.moego.server.customer.mapper.MoeCustomerAddressMapper;
import com.moego.server.customer.mapper.MoeCustomerContactMapper;
import com.moego.server.customer.mapper.MoeCustomerFilterMapper;
import com.moego.server.customer.mapper.MoeCustomerPetMapper;
import com.moego.server.customer.mapper.MoeCustomerTagBindingMapper;
import com.moego.server.customer.mapper.MoePetPetCodeBindingMapper;
import com.moego.server.customer.mapper.MoePetPetVaccineBindingMapper;
import com.moego.server.customer.mapperbean.MoeBusinessCustomer;
import com.moego.server.customer.mapperbean.MoeCustomerFilter;
import com.moego.server.customer.mapperbean.MoeCustomerPet;
import com.moego.server.customer.mapperbean.MoeCustomerTagBinding;
import com.moego.server.customer.mapstruct.CustomerConverter;
import com.moego.server.customer.params.ClientListParams;
import com.moego.server.customer.params.event.CustomerEventParams;
import com.moego.server.customer.service.client.CustomerAppointmentService;
import com.moego.server.customer.service.client.CustomerMembershipService;
import com.moego.server.customer.service.client.CustomerMessageService;
import com.moego.server.customer.service.client.CustomerPaymentService;
import com.moego.server.customer.service.dto.CustomerInfoDTO;
import com.moego.server.customer.service.dto.FilterCollectionDTO;
import com.moego.server.customer.service.dto.PetCountTotalDto;
import com.moego.server.customer.service.dto.PetNameBreedDto;
import com.moego.server.customer.service.dto.PrimaryPhoneNumberDto;
import com.moego.server.customer.service.dto.SearchPetCondition;
import com.moego.server.customer.service.grpc.GrpcAddressService;
import com.moego.server.customer.service.grpc.GrpcClientPetSettingService;
import com.moego.server.customer.service.grpc.GrpcCustomerService;
import com.moego.server.customer.service.util.CustomerFilterConverter;
import com.moego.server.customer.service.util.CustomerInfoConverter;
import com.moego.server.customer.utils.SvcPermissionHelper;
import com.moego.server.customer.web.params.clients.ClientListBulkParams;
import com.moego.server.customer.web.vo.client.ClientFilterVO;
import com.moego.server.customer.web.vo.client.ClientListVO;
import com.moego.server.customer.web.vo.client.PageClientListVO;
import com.moego.server.grooming.api.IAbandonRecordService;
import com.moego.server.grooming.dto.CustomerApptCountDTO;
import com.moego.server.grooming.dto.CustomerApptDateDTO;
import com.moego.server.message.api.IReviewBoosterService;
import com.moego.server.payment.dto.CustomerPaymentDTO;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/3/22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerListService {

    @Autowired
    private MoeBusinessCustomerMapper businessCustomerMapper;

    @Autowired
    private MoeCustomerPetMapper customerPetMapper;

    @Autowired
    private MoeCustomerTagBindingMapper customerTagBindingMapper;

    @Autowired
    private MoeCustomerContactMapper customerContactMapper;

    @Autowired
    private MoeCustomerAddressMapper customerAddressMapper;

    @Autowired
    private MoeCustomerFilterMapper customerFilterMapper;

    @Autowired
    private MoePetPetCodeBindingMapper petCodeBindingMapper;

    @Autowired
    private MoePetPetVaccineBindingMapper petVaccineBindingMapper;

    @Autowired
    private IBusinessBusinessService businessService;

    @Autowired
    private IBusinessServiceAreaService businessServiceAreaService;

    @Autowired
    private IReviewBoosterService reviewBoosterService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private CustomerPaymentService customerPaymentService;

    @Autowired
    private CustomerContactService customerContactService;

    @Autowired
    private CustomerPetService customerPetService;

    @Autowired
    private CustomerAppointmentService customerAppointmentService;

    @Autowired
    private CustomerMessageService customerMessageService;

    @Autowired
    private IAbandonRecordService abandonRecordService;

    @Autowired
    private ActiveMQService activeMQService;

    @Autowired
    private CustomerMembershipService customerMembershipService;

    @Autowired
    private AccountServiceBlockingStub accountService;

    @Autowired
    private CustomerAvailabilityServiceGrpc.CustomerAvailabilityServiceBlockingStub customerAvailabilityService;

    @Autowired
    private CustomerAvailabilityServiceGrpc.CustomerAvailabilityServiceBlockingStub
            customerAvailabilityServiceBlockingStub;

    @Autowired
    private IOperatorRule operatorRule;

    @Autowired
    private GrpcClientPetSettingService grpcClientPetSettingService;

    @Autowired
    private GrpcAddressService grpcAddressService;

    @Autowired
    private CompanyServiceGrpc.CompanyServiceBlockingStub companyServiceBlockingStub;

    @Autowired
    private GrpcCustomerService grpcCustomerService;

    @Autowired
    private SvcPermissionHelper permissionHelper;

    @Autowired
    private LockManager lockManager;

    @Value("${clients.view.max-title-length}")
    private int clientViewTitleMaxLength;

    @Value("${clients.view.max-count}")
    private int clientViewMaxCount;

    @Autowired
    @Qualifier("smartClientListExecutorService")
    private ExecutorService executor;

    public void initFilterView(long companyId, long staffId) {
        final var lockKey = lockManager.getResourceKey("ClientView", companyId + ":" + staffId);
        final var lockValue = CommonUtil.getUuid();
        try {
            if (lockManager.lock(lockKey, lockValue, 5L)) {
                long n = customerFilterMapper.countFilterView(companyId, staffId, true);
                if (0 < n) {
                    log.info("companyId: {}, staffId: {}, got client view: {}", companyId, staffId, n);
                    return;
                }
                List<ClientFilterVO> filters = List.of(
                        buildAllClientsView(companyId, staffId),
                        buildNeedRebookClientView(companyId, staffId),
                        buildUnpaidClientView(companyId, staffId),
                        buildThisWeekClientView(companyId, staffId));

                n = 0;
                for (var view : filters) {
                    var entity = toMoeCustomerFilter(view);
                    n += customerFilterMapper.insertFilterView(entity);
                }

                log.info("companyId: {}, staffId: {}, init {} client views.", companyId, staffId, n);
            }
        } catch (Exception e) {
            log.error("init client view for {}:{} occur EXCEPTION:", companyId, staffId, e);
        } finally {
            lockManager.unlock(lockKey, lockValue);
        }
    }

    public long countFilterView(long companyId, long staffId) {
        return customerFilterMapper.countFilterView(companyId, staffId, true);
    }

    public List<ClientFilterVO> getFilterViews(long companyId, long staffId, Boolean isDefault) {
        var entities = customerFilterMapper.selectFilterViews(companyId, staffId, isDefault, true);
        return entities.stream().map(CustomerListService::toClientFilterVO).toList();
    }

    public ClientFilterVO getFilterView(long companyId, long staffId, long id) {
        var entity = customerFilterMapper.selectFilterView(companyId, staffId, id, true);
        if (entity == null) {
            return null;
        }

        return toClientFilterVO(entity);
    }

    public int deleteFilterView(long companyId, long staffId, long id) {
        var entity = customerFilterMapper.selectFilterView(companyId, staffId, id, true);
        if (entity == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "client view not existing");
        }
        if (entity.getIsDefault()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "default client view cannot be deleted");
        }
        return customerFilterMapper.deleteFilterView(companyId, staffId, id);
    }

    public int updateFilterView(ClientFilterVO filter) {
        if (StringUtils.hasText(filter.title())
                && clientViewTitleMaxLength < filter.title().length()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "view name too long");
        }

        var entities = customerFilterMapper.selectFilterViews(filter.companyId(), filter.staffId(), null, true);
        if (entities == null || entities.isEmpty()) {
            return 0;
        }
        var existingFilter = entities.stream()
                .filter(entity -> entity.getId().equals(filter.id()))
                .findAny()
                .orElse(null);
        if (existingFilter == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "filter view not found");
        }
        if (existingFilter.getIsDefault()) {
            if (filter.filter() != null || filter.title() != null) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "default view cannot be updated");
            }
        }
        if (StringUtils.hasText(filter.title())) {
            for (var entity : entities) {
                if (!entity.getId().equals(filter.id()) && filter.title().equalsIgnoreCase(entity.getTitle())) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "view name already in use");
                }
            }
        }
        return customerFilterMapper.updateFilterView(toMoeCustomerFilter(filter));
    }

    public Long addFilterView(ClientFilterVO filter) {
        if (StringUtils.hasText(filter.title())
                && clientViewTitleMaxLength < filter.title().length()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "view name too long");
        }

        var entities = customerFilterMapper.selectFilterViews(filter.companyId(), filter.staffId(), null, true);
        if (entities != null && !entities.isEmpty()) {
            if (clientViewMaxCount <= entities.size()) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "view count exceeds the limit");
            }

            if (entities.stream().anyMatch(entity -> entity.getTitle().equalsIgnoreCase(filter.title()))) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "view name already in use");
            }
        }
        var entity = toMoeCustomerFilter(filter);
        customerFilterMapper.insertFilterView(entity);
        return entity.getId();
    }

    /**
     * List all customer by filter
     *
     * @param clientListParams businessId, queries, filters
     * @param businessDateTime business date time
     * @return all customer id
     */
    public Set<Integer> listAllCustomerIdByCustomProperty(
            long companyId,
            Integer businessId,
            Set<Integer> preferredBusinessIds,
            ClientListParams clientListParams,
            BusinessDateTimeDTO businessDateTime) {
        QueryParams queries = clientListParams.queries();
        // Check filter group depth
        FilterParams filterParams = clientListParams.filters();
        if (FilterUtils.checkFilterDepth(filterParams, 1)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "filter group depth is too deep");
        }
        // Get customer id by keyword
        Set<Integer> queryCustomerIdsBykeyword = new HashSet<>();
        if (Objects.nonNull(queries) && StringUtils.hasText(queries.keyword())) {
            // DONE(account structure)
            queryCustomerIdsBykeyword = handleKeyword(companyId, preferredBusinessIds, queries);
            if (CollectionUtils.isEmpty(queryCustomerIdsBykeyword)) {
                return Collections.emptySet();
            }
        }

        Set<Integer> queryCustomerIdsByFilter = new HashSet<>();
        // Replace filter params while contains client_status, client_type, pet_vaccine
        if (Objects.nonNull(filterParams)) {
            filterParams = CustomerFilterConverter.replaceFilterParams(filterParams);
            var businessClientsDTO = new BusinessClientsDTO(
                    true, companyId, businessId, preferredBusinessIds, queryCustomerIdsBykeyword);
            var businessDateClientsDTO = new BusinessDateClientsDTO(businessClientsDTO, businessDateTime);
            queryCustomerIdsByFilter = handleFilter(businessDateClientsDTO, filterParams, new FilterCollectionDTO());
        } else if (CollectionUtils.isEmpty(queryCustomerIdsBykeyword)) {
            var filterDTO = ClientsFilterDTO.builder()
                    .companyId(companyId)
                    .preferredBusinessIds(preferredBusinessIds)
                    .build();
            queryCustomerIdsByFilter = businessCustomerMapper.listCustomerIdByFilter(filterDTO);
        } else {
            queryCustomerIdsByFilter = queryCustomerIdsBykeyword;
        }

        if (CollectionUtils.isEmpty(queryCustomerIdsByFilter)) {
            return Collections.emptySet();
        } else {
            return customerService.batchGetValidCustomerId(companyId, new ArrayList<>(queryCustomerIdsByFilter));
        }
    }

    public BusinessDateTimeDTO getCompanyTimeZone(long companyId) {
        // Get business time zone for date filter
        var preferenceSettingRequest = GetCompanyPreferenceSettingRequest.newBuilder()
                .setCompanyId(companyId)
                .build();
        var timeZoneName = companyServiceBlockingStub
                .getCompanyPreferenceSetting(preferenceSettingRequest)
                .getPreferenceSetting()
                .getTimeZone()
                .getName();

        var now = LocalDateTime.now(ZoneId.of(timeZoneName));
        return new BusinessDateTimeDTO()
                .setCompanyId(companyId)
                .setBusinessId(null)
                .setLocalDateTime(now)
                .setCurrentDate(now.toLocalDate().toString())
                .setCurrentMinutes(now.get(ChronoField.MINUTE_OF_DAY))
                .setTimezoneName(timeZoneName);
    }

    public BusinessDateTimeDTO getBusinessTimeZone(long companyId, Integer businessId) {
        var dto = businessService.getBusinessDateTime(businessId);
        return new BusinessDateTimeDTO()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setLocalDateTime(dto.getLocalDateTime())
                .setCurrentDate(dto.getCurrentDate())
                .setCurrentMinutes(dto.getCurrentMinutes())
                .setTimezoneName(dto.getTimezoneName());
    }

    /**
     * List customer by custom property
     *
     * @param businessId       business id
     * @param clientListParams client list params
     * @return customer list
     */
    @TimerMetrics
    public PageClientListVO listCustomerByCustomProperty(
            long companyId, Integer businessId, Set<Integer> preferredBusinessIds, ClientListParams clientListParams) {
        // Get business time zone for date filter
        BusinessDateTimeDTO dateTime = getCompanyTimeZone(companyId);
        // Get all customer id by custom property
        // TODO: check submit task
        Set<Integer> customerIds = listAllCustomerIdByCustomProperty(
                companyId, businessId, preferredBusinessIds, clientListParams, dateTime);
        if (CollectionUtils.isEmpty(customerIds)) {
            return PageClientListVO.builder()
                    .clientPage(PageDTO.createEmpty())
                    .petCount(Collections.emptyList())
                    .build();
        }
        // Get pet count
        CompletableFuture<List<PetCountTotalDto>> petCountFuture = CompletableFuture.supplyAsync(
                () -> countPetByCustomerIds(companyId, preferredBusinessIds, customerIds), executor);
        // Order by and pagination
        List<Integer> sortCustomerIds = sortByAndPagination(
                true, companyId, businessId, preferredBusinessIds, customerIds, clientListParams, dateTime);
        // Get customer info
        List<ClientListVO> clientListVOList =
                listClientListInfo(companyId, businessId, preferredBusinessIds, sortCustomerIds, dateTime);
        return PageClientListVO.builder()
                .clientPage(PageDTO.create(
                        clientListVOList, customerIds.size(), clientListParams.pageNum(), clientListParams.pageSize()))
                .petCount(Optional.ofNullable(petCountFuture.join()).orElse(Collections.emptyList()))
                .build();
    }

    public List<PetCountTotalDto> countPetByCustomerIds(
            long companyId, Set<Integer> preferredBusinessIds, Set<Integer> customerIds) {
        var result = customerPetMapper.countPetByCustomerIds(companyId, preferredBusinessIds, customerIds);
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }

        var petTypeNames = grpcClientPetSettingService.getPetTypeNameMap(companyId, null);
        result.forEach(dto -> {
            var petTypeName = petTypeNames.getOrDefault(dto.getPetTypeId(), "");
            dto.setTypeName(petTypeName);
        });

        return result;
    }

    /**
     * this method is used to support searches in the form of '{pet_name} {client_last_name}'(or vice versa)
     * it will find the customer with last name=${last name}, and this customer has a pet name=${pet name}
     * @param companyId company id
     * @param businessIds business ids
     * @param queryParams query params
     * @return customer id list
     */
    public List<Integer> handleKeywordPetClient(long companyId, Set<Integer> businessIds, QueryParams queryParams) {
        if (Objects.isNull(queryParams)) {
            return Collections.emptyList();
        }
        var keyword = queryParams.keyword();
        return handleKeywordPetClient(companyId, businessIds, keyword);
    }

    public List<Integer> handleKeywordPetClient(long companyId, Set<Integer> businessIds, String keyword) {
        return handleKeywordPetClient(
                companyId, businessIds, keyword, this.businessCustomerMapper, this.customerPetMapper);
    }

    public static List<Integer> handleKeywordPetClient(
            long companyId,
            Set<Integer> businessIds,
            String keyword,
            MoeBusinessCustomerMapper businessCustomerMapper,
            MoeCustomerPetMapper customerPetMapper) {
        if (keyword.split(" ").length < 2) {
            return Collections.emptyList();
        }
        var customers = businessCustomerMapper.queryCustomerIdByKeywordFullText(companyId, businessIds, keyword);
        var spc = new SearchPetCondition();
        spc.setTerm(keyword);
        spc.setCompanyId(companyId);
        spc.setBusinessIds(businessIds);
        var pets = customerPetMapper.searchPetV2(spc);

        var petCustomerIds = pets.stream().map(MoeCustomerPet::getCustomerId).collect(Collectors.toSet());
        return customers.stream().filter(petCustomerIds::contains).distinct().toList();
    }

    /**
     * Query customer id by keyword
     *
     * @param queryParams query params
     * @return customer id set
     */
    public Set<Integer> handleKeyword(long companyId, Set<Integer> preferredBusinessIds, QueryParams queryParams) {
        if (Objects.isNull(queryParams)) {
            return Collections.emptySet();
        }

        String keyword = queryParams.keyword();
        List<CompletableFuture<List<Integer>>> queryFutureList = new ArrayList<>();
        // support '{pet_name} {client_last_name}', CRM-1639
        queryFutureList.add(CompletableFuture.supplyAsync(
                () -> handleKeywordPetClient(companyId, preferredBusinessIds, queryParams), executor));
        queryFutureList.add(CompletableFuture.supplyAsync(
                () -> {
                    // query client name and email
                    return businessCustomerMapper.queryCustomerIdByKeyword(companyId, preferredBusinessIds, keyword);
                },
                executor));
        queryFutureList.add(CompletableFuture.supplyAsync(
                () -> {
                    // query pet name and breed
                    return customerPetMapper.getCustomerPetByFindPetNameAndBreed(
                            companyId, preferredBusinessIds, keyword);
                },
                executor));
        queryFutureList.add(CompletableFuture.supplyAsync(
                () -> {
                    // query phone number, email, name
                    return customerContactMapper.getCustomerIdByFindPhoneNumber(
                            companyId,
                            preferredBusinessIds,
                            keyword,
                            processKeywordForPhoneNumberSearch(keyword),
                            keyword.replace(" ", ""));
                },
                executor));
        queryFutureList.add(CompletableFuture.supplyAsync(
                () -> {
                    // query address
                    return customerAddressMapper.queryCustomerIdByKeyword(companyId, preferredBusinessIds, keyword);
                },
                executor));
        CompletableFuture.allOf(queryFutureList.toArray(new CompletableFuture[0]))
                .join();
        // Get filter result set
        Set<Integer> queryCustomerIds = new HashSet<>();
        // collect customer id
        queryFutureList.forEach(queryFuture -> {
            try {
                List<Integer> queryResult = queryFuture.join();
                if (!CollectionUtils.isEmpty(queryResult)) {
                    queryCustomerIds.addAll(queryResult);
                }
            } catch (Exception e) {
                log.error("query customer id by keyword error", e);
            }
        });
        return queryCustomerIds;
    }

    static String processKeywordForPhoneNumberSearch(String keyword) {
        return keyword
                // see https://moego.atlassian.net/browse/ERP-786
                .replace("(", "")
                .replace(")", "")
                .replace("+1", "")
                .replace(" ", "")
                .replace("/", "")
                .replace(".", "")
                // see https://moego.atlassian.net/browse/ERP-2779
                .replace("+", "")
                .replace("-", "");
    }

    /**
     * List customer info
     *
     * @param businessId       business id
     * @param customerIds      sorted customer id list
     * @param businessDateTime business date time
     * @return customer info list
     */
    public List<ClientListVO> listClientListInfo(
            long companyId,
            Integer businessId,
            Set<Integer> preferredBusinessIds,
            List<Integer> customerIds,
            BusinessDateTimeDTO businessDateTime) {

        if (CollectionUtils.isEmpty(customerIds)) {
            return List.of();
        }

        BusinessClientsDTO businessClientsDTO = BusinessClientsDTO.builder()
                .migrated(true)
                .companyId(companyId)
                // 兼容 remote client filter 设置 business id
                .businessId(businessId)
                .preferredBusinessIds(preferredBusinessIds)
                .customerIds(new HashSet<>(customerIds))
                .build();
        var customerIdToInfoFuture =
                CompletableFuture.supplyAsync(() -> customerService.listCustomerInfo(customerIds), executor);
        // TODO: check company result
        var customerIdToPaymentFuture = CompletableFuture.supplyAsync(
                () -> customerPaymentService.listCustomerPayment(businessClientsDTO), executor);
        var customerIdToPrimaryPhoneFuture = CompletableFuture.supplyAsync(
                () -> customerContactService.listCustomerPrimaryPhoneNumber(companyId, customerIds), executor);
        var customerIdToPetInfosFuture = CompletableFuture.supplyAsync(
                () -> customerPetService.listPetNameBreedByCustomerId(companyId, customerIds), executor);
        BusinessDateClientsDTO businessDateClientsDTO = BusinessDateClientsDTO.builder()
                .businessClients(businessClientsDTO)
                .businessDateTime(businessDateTime)
                .build();
        // TODO: check company result
        var customerIdToApptCountFuture = CompletableFuture.supplyAsync(
                () -> customerAppointmentService.listCustomerApptCount(businessDateClientsDTO), executor);
        // TODO: check company result
        var customerIdToApptDateFuture = CompletableFuture.supplyAsync(
                () -> customerAppointmentService.listCustomerApptDate(businessDateClientsDTO), executor);
        var customerIdToMembershipFuture = CompletableFuture.supplyAsync(
                () -> customerMembershipService.listCustomerMembership(companyId, customerIds), executor);
        var accountIdToAccountFuture = listAccounts(customerIdToInfoFuture);

        // Get blocked service item types
        var listBlockCustomerFuture = CompletableFuture.supplyAsync(
                () -> customerAvailabilityServiceBlockingStub
                        .listBlockedCustomer(ListBlockedCustomerRequest.newBuilder()
                                .setCompanyId(companyId)
                                .addAllServiceItemTypes(List.of(
                                        ServiceItemType.GROOMING, ServiceItemType.BOARDING, ServiceItemType.DAYCARE))
                                .addAllCustomerIds(customerIds.stream()
                                        .map(Integer::longValue)
                                        .toList())
                                .setPagination(PaginationRequest.newBuilder()
                                        .setPageNum(1)
                                        .setPageSize(1000)
                                        .build())
                                .build())
                        .getCustomerBlockInfosList()
                        .stream()
                        .collect(Collectors.toMap(
                                ListBlockedCustomerResponse.CustomerBlockInfo::getCustomerId, Function.identity())),
                executor);

        CompletableFuture.allOf(
                        customerIdToInfoFuture,
                        customerIdToPaymentFuture,
                        customerIdToPrimaryPhoneFuture,
                        customerIdToPetInfosFuture,
                        customerIdToApptCountFuture,
                        customerIdToApptDateFuture,
                        customerIdToMembershipFuture,
                        accountIdToAccountFuture,
                        listBlockCustomerFuture)
                .join();

        return customerIds.stream()
                .map(customerId -> {
                    CustomerInfoDTO customerInfoDTO = customerIdToInfoFuture
                            .join()
                            .getOrDefault(customerId, CustomerInfoDTO.builder().build());
                    CustomerPaymentDTO customerPaymentDTO = customerIdToPaymentFuture
                            .join()
                            .getOrDefault(
                                    customerId,
                                    CustomerPaymentDTO.builder()
                                            .totalPaidAmount(BigDecimal.ZERO)
                                            .build());
                    PrimaryPhoneNumberDto primaryPhoneNumberDto =
                            customerIdToPrimaryPhoneFuture.join().getOrDefault(customerId, new PrimaryPhoneNumberDto());
                    List<PetNameBreedDto> petNameBreedDtoList =
                            customerIdToPetInfosFuture.join().getOrDefault(customerId, Collections.emptyList());
                    CustomerApptCountDTO customerApptCountDTO = customerIdToApptCountFuture
                            .join()
                            .getOrDefault(
                                    customerId,
                                    CustomerApptCountDTO.builder()
                                            .totalApptCount(0)
                                            .build());
                    CustomerApptDateDTO customerApptDateDTO = customerIdToApptDateFuture
                            .join()
                            .getOrDefault(
                                    customerId, CustomerApptDateDTO.builder().build());
                    String expectedService = CustomerInfoConverter.getExpectedService(
                            customerApptDateDTO.getLastApptDate(), customerInfoDTO.preferredFrequencyDay());

                    List<ServiceItemType> blockedServiceItemTypes = new ArrayList<>();
                    Optional.ofNullable(listBlockCustomerFuture.join().get(customerId.longValue()))
                            .ifPresent(customerBlockInfo ->
                                    blockedServiceItemTypes.addAll(customerBlockInfo.getServiceItemTypesList()));

                    return ClientListVO.builder()
                            .customerId(customerId)
                            .firstName(customerInfoDTO.firstName())
                            .lastName(customerInfoDTO.lastName())
                            .avatarPath(customerInfoDTO.avatarPath())
                            .preferredBusinessId(customerInfoDTO.preferredBusinessId())
                            .clientColor(customerInfoDTO.clientColor())
                            .phoneNumber(primaryPhoneNumberDto.getPhoneNumber())
                            .email(customerInfoDTO.email())
                            .preferredFrequencyType(customerInfoDTO.preferredFrequencyType())
                            .preferredFrequencyDay(customerInfoDTO.preferredFrequencyDay())
                            .petList(petNameBreedDtoList)
                            .totalPaid(customerPaymentDTO.getTotalPaidAmount())
                            .totalApptCount(customerApptCountDTO.getTotalApptCount())
                            .overdue(CustomerInfoConverter.getOverdue(
                                    expectedService,
                                    businessDateTime.getLocalDateTime().toLocalDate()))
                            .lastServiceTime(customerApptDateDTO.getLastApptDate())
                            .expectedServiceTime(expectedService)
                            .upcomingBooking(customerApptDateDTO.getNextApptDate())
                            .isNewCustomer(
                                    CustomerInfoConverter.isNewCustomer(customerApptCountDTO.getFinishedApptCount()))
                            .isProspectCustomer(CustomerInfoConverter.isProspectCustomer(
                                    customerApptCountDTO.getTotalApptAndRequestsCount(), customerInfoDTO.source()))
                            .hasPetParentAppAccount(CustomerInfoConverter.hasPetParentAppAccount(
                                    accountIdToAccountFuture.join().get(customerInfoDTO.accountId())))
                            .inactive(customerInfoDTO.inactive())
                            .isUnsubscribed(customerInfoDTO.isUnsubscribed())
                            .membershipSubscriptions(customerIdToMembershipFuture
                                    .join()
                                    .getOrDefault(customerId, Collections.emptyList()))
                            .blockedServiceItemTypes(blockedServiceItemTypes)
                            .build();
                })
                .toList();
    }

    private CompletableFuture<Map<Long, AccountModel>> listAccounts(
            CompletableFuture<Map<Integer, CustomerInfoDTO>> customerIdToInfoFuture) {
        return customerIdToInfoFuture.thenApplyAsync(
                result -> {
                    var accountIds = result.values().stream()
                            .map(CustomerInfoDTO::accountId)
                            .filter(accountId -> !PrimitiveTypeUtil.isNullOrZero(accountId))
                            .collect(Collectors.toSet());
                    if (CollectionUtils.isEmpty(accountIds)) {
                        return Map.of();
                    }

                    // size * 2 避免 hash map 发生扩容
                    var accountMap = new HashMap<Long, AccountModel>(accountIds.size() * 2);
                    Lists.partition(new ArrayList<>(accountIds), 1000).forEach(ids -> {
                        var accounts = accountService
                                .batchGetAccount(BatchGetAccountRequest.newBuilder()
                                        .addAllIds(ids)
                                        .build())
                                .getAccountsList();
                        if (!CollectionUtils.isEmpty(accounts)) {
                            accounts.forEach(account -> accountMap.put(account.getId(), account));
                        }
                    });
                    return accountMap;
                },
                executor);
    }

    /**
     * Decide whether to intersect or union a set of filter result sets by type
     *
     * @param businessDateClientsDTO business id, customer id set and business date time
     * @param filterParams           filter or filter group
     * @param filterCollectionDTO    filter collection DTO, contains all table filter
     */
    public Set<Integer> handleFilter(
            BusinessDateClientsDTO businessDateClientsDTO,
            FilterParams filterParams,
            FilterCollectionDTO filterCollectionDTO) {
        if (FilterUtils.isFilter(filterParams)) {
            // Collect filter
            FilterDTO filterDTO;
            if (Objects.equals(filterParams.property(), PropertyEnum.pet_size)) {
                var migrated = businessDateClientsDTO.businessClients().migrated();
                var companyId = businessDateClientsDTO.businessClients().companyId();
                var businessId = migrated
                        ? null
                        : businessDateClientsDTO.businessClients().businessId();

                var petSizeMap = grpcClientPetSettingService.getPetSizeList(companyId, businessId);

                filterDTO = CustomerFilterConverter.petSizeFilterToPetWeightFilterDTO(
                        filterParams, petSizeMap, operatorRule);
            } else if (Objects.equals(filterParams.property(), PropertyEnum.lapsed_client)) {
                filterDTO = CustomerFilterConverter.lapsedClientFilterToFilterDTO(
                        filterParams, businessDateClientsDTO.businessDateTime().getCurrentDate(), operatorRule);
                // Lapsed client needs to be added to active client filter
                FilterDTO activeFilter = CustomerFilterConverter.filterToFilterDTO(
                        FilterParams.builder()
                                .property(PropertyEnum.inactive_client)
                                .operator(OperatorEnum.OPERATOR_EQUAL)
                                .value(String.valueOf(BooleanEnum.VALUE_FALSE))
                                .build(),
                        operatorRule);
                filterCollectionDTO
                        .getTableFilterMap()
                        .get(activeFilter.getProperty().getTable())
                        .add(activeFilter);
            } else if (Objects.equals(filterParams.property(), PropertyEnum.review_rating)) {
                filterDTO = CustomerFilterConverter.filterToFilterDTO(filterParams, operatorRule);
                FilterDTO reviewCntFilter = CustomerFilterConverter.filterToFilterDTO(
                        FilterParams.builder()
                                .property(PropertyEnum.review_cnt)
                                .operator(OperatorEnum.OPERATOR_GREATER_THAN)
                                .value(CustomerFilterConverter.VALUE_ZERO)
                                .build(),
                        operatorRule);
                filterCollectionDTO
                        .getTableFilterMap()
                        .get(reviewCntFilter.getProperty().getTable())
                        .add(reviewCntFilter);
            } else if (Objects.equals(filterParams.property(), PropertyEnum.abandoned_date)) {
                filterDTO = CustomerFilterConverter.abandonedDateFilterToFilterDTO(
                        filterParams, businessDateClientsDTO.businessDateTime(), operatorRule);
            } else if (Objects.equals(filterParams.property(), PropertyEnum.last_contact_time)) {
                filterDTO = CustomerFilterConverter.lastContactTimeFilterToFilterDTO(
                        filterParams, businessDateClientsDTO.businessDateTime(), operatorRule);
            } else if (Objects.equals(filterParams.property(), PropertyEnum.cof_status)) {
                // 计算过期日期
                // cof_status 是 has_cof 是同一个张表， 两个 filter 不完全独立，所以在 payment 实现层会判断两个的逻辑关系
                filterDTO = CustomerFilterConverter.cofStatusFilterToFilterDTO(
                        filterParams, businessDateClientsDTO.businessDateTime(), operatorRule);
            } else if (Objects.equals(filterParams.property(), PropertyEnum.cof_request)) {
                filterDTO = CustomerFilterConverter.cofRequestFilterToFilterDTO(
                        filterParams, businessDateClientsDTO.businessDateTime(), filterCollectionDTO, operatorRule);
            } else if (Objects.equals(filterParams.property().getType(), PropertyTypeEnum.DATE)) {
                filterDTO = CustomerFilterConverter.dateFilterToFilterDTO(
                        filterParams, businessDateClientsDTO.businessDateTime(), operatorRule);
            } else if (Objects.equals(filterParams.property(), PropertyEnum.creation_date)) {
                filterDTO = CustomerFilterConverter.creationDateFilterToFilterDTO(
                        filterParams, businessDateClientsDTO.businessDateTime(), operatorRule);
            } else {
                filterDTO = CustomerFilterConverter.filterToFilterDTO(filterParams, operatorRule);
            }
            filterCollectionDTO
                    .getTableFilterMap()
                    .get(filterDTO.getProperty().getTable())
                    .add(filterDTO);
            return Collections.emptySet();
        } else if (FilterUtils.isFilterGroup(filterParams)) {
            // Handle filter group
            return handleFilterGroup(businessDateClientsDTO, filterParams, filterCollectionDTO);
        } else {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "filterParams type error");
        }
    }

    /**
     * Handle filter group
     *
     * @param businessDateClientsDTO business id, customer id set and business date time
     * @param filterGroup            filter group
     * @param filterCollectionDTO    filter collection DTO, contains all table filter
     * @return filter group result set
     */
    public Set<Integer> handleFilterGroup(
            BusinessDateClientsDTO businessDateClientsDTO,
            FilterParams filterGroup,
            FilterCollectionDTO filterCollectionDTO) {
        if (!FilterUtils.isFilterGroup(filterGroup)) {
            return Collections.emptySet();
        }
        List<CompletableFuture<Set<Integer>>> filterGroupFutureList = new ArrayList<>();
        filterGroup.filters().forEach(filter -> {
            if (FilterUtils.isFilterGroup(filter)) {
                filterGroupFutureList.add(CompletableFuture.supplyAsync(
                        () -> handleFilterGroup(businessDateClientsDTO, filter, new FilterCollectionDTO()), executor));
            } else if (FilterUtils.isFilter(filter)) {
                handleFilter(businessDateClientsDTO, filter, filterCollectionDTO);
            } else {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "filterParams type error");
            }
        });
        // No filter and filter group
        if (filterCollectionDTO.isEmpty() && filterGroupFutureList.isEmpty()) {
            return Collections.emptySet();
        }
        // Remote call filter by count need query all customer id
        Set<Integer> allCustomerIds = Collections.emptySet();
        if (filterCollectionDTO.hasQueryAllCustomerId()
                && CollectionUtils.isEmpty(
                        businessDateClientsDTO.businessClients().customerIds())) {

            var companyId = businessDateClientsDTO.businessClients().companyId();
            var preferredBusinessIds = businessDateClientsDTO.businessClients().preferredBusinessIds();

            var filterDTO = ClientsFilterDTO.builder()
                    .companyId(companyId)
                    .preferredBusinessIds(preferredBusinessIds)
                    .build();
            allCustomerIds = businessCustomerMapper.listCustomerIdByFilter(filterDTO);
        }
        filterGroupFutureList.addAll(
                submitTask(businessDateClientsDTO, filterGroup.type(), filterCollectionDTO, allCustomerIds));
        CompletableFuture.allOf(filterGroupFutureList.toArray(new CompletableFuture[0]))
                .join();
        // Get filter result set
        List<Set<Integer>> filterGroupSetList = new ArrayList<>();
        filterGroupFutureList.forEach(future -> {
            try {
                filterGroupSetList.add(future.join());
            } catch (Exception e) {
                log.error("Get filter result set error", e);
            }
        });
        // Intersect or union
        if (Objects.equals(filterGroup.type(), TypeEnum.TYPE_AND)) {
            return filterGroupSetList.stream()
                    .reduce((a, b) -> {
                        a.retainAll(b);
                        return a;
                    })
                    .orElse(Collections.emptySet());
        } else if (Objects.equals(filterGroup.type(), TypeEnum.TYPE_OR)) {
            return filterGroupSetList.stream().flatMap(Set::stream).collect(Collectors.toSet());
        } else {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "filterGroup type error");
        }
    }

    public List<CompletableFuture<Set<Integer>>> submitTask(
            BusinessDateClientsDTO businessDateClientsDTO,
            TypeEnum type,
            FilterCollectionDTO filterCollectionDTO,
            Set<Integer> allCustomerIds) {

        var companyId = businessDateClientsDTO.businessClients().companyId();
        var businessId = businessDateClientsDTO.businessClients().businessId();
        var preferredBusinessIds = businessDateClientsDTO.businessClients().preferredBusinessIds();
        var customerIds = businessDateClientsDTO.businessClients().customerIds();

        List<CompletableFuture<Set<Integer>>> filterGroupFutureList = new ArrayList<>();
        filterCollectionDTO.getTableFilterMap().entrySet().stream()
                .filter(entry -> !CollectionUtils.isEmpty(entry.getValue()))
                .forEach(entry -> filterGroupFutureList.add(CompletableFuture.supplyAsync(
                        () -> {
                            ClientsFilterDTO.ClientsFilterDTOBuilder filterDTOBuilder = ClientsFilterDTO.builder()
                                    .companyId(companyId)
                                    .filters(entry.getValue())
                                    .type(type)
                                    .connector(Objects.equals(type, TypeEnum.TYPE_OR) ? "OR" : "AND")
                                    .businessDateTime(businessDateClientsDTO.businessDateTime());
                            ClientsFilterDTO clientsFilter = filterDTOBuilder
                                    .preferredBusinessIds(preferredBusinessIds)
                                    .customerIds(customerIds)
                                    .build();
                            // 1. 一般情况下， 有 reverse 操作的才需要传整个location 的 customer id 列表（as 下就是 company 的）
                            // 2. service area / customer for ob service / membership 处理 customerIds 数组比较特殊， 需要单独处理
                            boolean passAllCustomers = entry.getValue().stream().anyMatch(FilterDTO::isReversed)
                                    || Objects.equals(entry.getKey(), PropertyTableEnum.SERVICE_AREA)
                                    || Objects.equals(entry.getKey(), PropertyTableEnum.CUSTOMER_FOR_ONLINE_BOOKING)
                                    || Objects.equals(entry.getKey(), PropertyTableEnum.MEMBERSHIP);
                            ClientsFilterDTO remoteClientsFilter = filterDTOBuilder
                                    // 对于 remote 查询，
                                    // 迁移后查询 company 维度数据，不需要根据 preferredBusinessIds 过滤
                                    // 迁移前则把 businessId 当作 preferredBusinessIds 进行过滤
                                    .preferredBusinessIds(null)
                                    .customerIds(
                                            CollectionUtils.isEmpty(customerIds) && (passAllCustomers)
                                                    ? allCustomerIds
                                                    : customerIds)
                                    .build();
                            return switch (entry.getKey()) {
                                case CUSTOMER -> businessCustomerMapper.listCustomerIdByFilter(clientsFilter);
                                case TAG -> customerTagBindingMapper.listCustomerIdByFilter(clientsFilter);
                                case ADDRESS -> customerAddressMapper.listCustomerIdByFilter(clientsFilter);
                                case ADDRESS_COUNT -> customerAddressMapper.listCustomerIdByCountFilter(clientsFilter);
                                case CONTACT -> customerContactMapper.listCustomerIdByFilter(clientsFilter);
                                case CONTACT_COUNT -> customerContactMapper.listCustomerIdByCountFilter(clientsFilter);
                                case PET -> customerPetMapper.listCustomerIdByFilter(clientsFilter);
                                case PET_COUNT -> customerPetMapper.listCustomerIdByCountFilter(clientsFilter);
                                case PET_CODE -> petCodeBindingMapper.listCustomerIdByFilter(clientsFilter);
                                case PET_VACCINE -> petVaccineBindingMapper.listCustomerIdByCountFilter(clientsFilter);
                                case APPT_COUNT -> customerAppointmentService.listCustomerIdByCountFilter(
                                        remoteClientsFilter);
                                case APPT_DATE -> customerAppointmentService.listCustomerIdByDateFilter(
                                        remoteClientsFilter);
                                case APPT_GROOMER -> customerAppointmentService.listCustomerIdByGroomerFilter(
                                        remoteClientsFilter);
                                case REVIEW_BOOSTER -> reviewBoosterService.listCustomerIdByFilter(remoteClientsFilter);
                                case PAYMENT -> customerPaymentService.listCustomerIdByFilter(remoteClientsFilter);
                                case PAYMENT_CREDIT_CARD -> customerPaymentService.listCustomerIdByCreditCardFilter(
                                        remoteClientsFilter);
                                case MESSAGE_CARD_LINK -> customerMessageService.listCustomerIdByFilter(
                                        remoteClientsFilter);
                                case ABANDONED_RECORD -> abandonRecordService.listCustomerIdByFilter(
                                        remoteClientsFilter.toBuilder()
                                                .businessId(businessId)
                                                .build());
                                case SERVICE_AREA -> clientFilterByServiceArea(businessId, remoteClientsFilter);
                                case CUSTOMER_FOR_ONLINE_BOOKING -> clientFilterByOnlineBookingSelectedService(
                                        remoteClientsFilter);
                                case MEMBERSHIP -> customerMembershipService.listCustomerIdByFilter(
                                        remoteClientsFilter);
                            };
                        },
                        executor)));
        return filterGroupFutureList;
    }

    /**
     * FIXME: business id 入参后面应该改为 company id
     */
    public Set<Integer> clientFilterByServiceArea(long businessId, ClientsFilterDTO clientsFilter) {
        if (CollectionUtils.isEmpty(clientsFilter.filters())
                || Objects.isNull(clientsFilter.type())
                || CollectionUtils.isEmpty(clientsFilter.customerIds())) {
            return Collections.emptySet();
        }
        FilterDTO filterDTO = clientsFilter.filters().get(0);

        boolean isOperatorIn;
        switch (filterDTO.getOperator()) {
            case OPERATOR_IN -> isOperatorIn = true;
            case OPERATOR_NOT_IN -> isOperatorIn = false;
            default -> {
                return Collections.emptySet();
            }
        }

        // 获取 待过滤的 service area id
        List<Integer> areaIds =
                filterDTO.getValues().stream().map(Integer::parseInt).toList();

        // 获取客户的 primary address
        var clientAddress = grpcAddressService
                .batchGetPrimaryAddressDto(clientsFilter.customerIds())
                .values();

        // 获取归属的 service area
        List<GetAreasByLocationParams> locations = clientAddress.stream()
                .map(address -> new GetAreasByLocationParams(
                        address.getCustomerId().longValue(), address.getLat(), address.getLng(), address.getZipcode()))
                .toList();
        // TODO: 后面需要开一个 company 维度的接口查询 service area
        Map<Long, List<CertainAreaDTO>> result = businessServiceAreaService.getAreasByLocation(
                new BatchGetAreasByLocationParams(businessId, areaIds, locations));

        // 过滤在 service area 内的客户
        Set<Integer> customIdsIn = result.entrySet().stream()
                .filter(entry -> !CollectionUtils.isEmpty(entry.getValue()))
                .map(entry -> entry.getKey().intValue())
                .collect(Collectors.toSet());

        // 判断是否取反
        if (isOperatorIn) {
            return customIdsIn;
        }
        return clientsFilter.customerIds().stream()
                .filter(k -> !customIdsIn.contains(k))
                .collect(Collectors.toSet());
    }

    private Set<Integer> clientFilterByOnlineBookingSelectedService(ClientsFilterDTO clientsFilter) {
        if (CollectionUtils.isEmpty(clientsFilter.filters())
                || Objects.isNull(clientsFilter.type())
                || CollectionUtils.isEmpty(clientsFilter.customerIds())) {
            return Collections.emptySet();
        }

        FilterDTO filterDTO = clientsFilter.filters().get(0);
        boolean isOperatorIn;
        switch (filterDTO.getOperator()) {
            case OPERATOR_IN -> isOperatorIn = true;
            case OPERATOR_NOT_IN -> isOperatorIn = false;
            default -> {
                return Collections.emptySet();
            }
        }

        List<Integer> serviceItemTypes =
                filterDTO.getValues().stream().map(Integer::parseInt).distinct().toList();

        int pageNum = 1;
        int pageSize = 1000;
        var resp = customerAvailabilityService.listBlockedCustomer(ListBlockedCustomerRequest.newBuilder()
                .addAllServiceItemTypes(serviceItemTypes.stream()
                        .map(ServiceItemType::forNumber)
                        .toList())
                .addAllCustomerIds(clientsFilter.customerIds().stream()
                        .map(Integer::longValue)
                        .toList())
                .setPagination(PaginationRequest.newBuilder()
                        .setPageNum(pageNum)
                        .setPageSize(pageSize)
                        .build())
                .setCompanyId(clientsFilter.companyId())
                .build());
        Set<Integer> customerIds = resp.getCustomerBlockInfosList().stream()
                .map(ListBlockedCustomerResponse.CustomerBlockInfo::getCustomerId)
                .map(Long::intValue)
                .collect(Collectors.toSet());
        if (resp.getPagination().getTotal() > pageSize) {
            int totalPage = (int) Math.ceil((double) resp.getPagination().getTotal() / pageSize);
            for (int i = 2; i <= totalPage; i++) {
                resp = customerAvailabilityService.listBlockedCustomer(ListBlockedCustomerRequest.newBuilder()
                        .addAllServiceItemTypes(serviceItemTypes.stream()
                                .map(ServiceItemType::forNumber)
                                .toList())
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(i)
                                .setPageSize(pageSize)
                                .build())
                        .setCompanyId(clientsFilter.companyId())
                        .build());
                customerIds.addAll(resp.getCustomerBlockInfosList().stream()
                        .map(ListBlockedCustomerResponse.CustomerBlockInfo::getCustomerId)
                        .map(Long::intValue)
                        .collect(Collectors.toSet()));
            }
        }

        if (isOperatorIn) {
            return customerIds;
        }
        return clientsFilter.customerIds().stream()
                .filter(k -> !customerIds.contains(k))
                .collect(Collectors.toSet());
    }

    /**
     * Sort by field and pagination
     *
     * @param businessId       business id
     * @param customerIds      to be sorted customer id set
     * @param clientListParams sort params and pagination params
     * @return sorted and paginated customer id set
     */
    public List<Integer> sortByAndPagination(
            boolean migrated,
            long companyId,
            Integer businessId,
            Set<Integer> preferredBusinessIds,
            Set<Integer> customerIds,
            ClientListParams clientListParams,
            BusinessDateTimeDTO businessDateTime) {
        SortParams sort = clientListParams.sort();
        if (Objects.isNull(sort)) {
            sort = new SortParams(PropertyEnum.client_id, PageQuery.OrderEnum.asc);
        }
        Integer offset = null;
        Integer limit = null;
        if (Objects.nonNull(clientListParams.pageNum()) && Objects.nonNull(clientListParams.pageSize())) {
            offset = (clientListParams.pageNum() - 1) * clientListParams.pageSize();
            limit = clientListParams.pageSize();
        }

        ClientsFilterDTO clientsFilterDTO = ClientsFilterDTO.builder()
                .companyId(companyId)
                .preferredBusinessIds(migrated ? preferredBusinessIds : Set.of(businessId))
                .customerIds(customerIds)
                .sort(sort)
                .offset(offset)
                .limit(limit)
                .build();
        return switch (sort.property()) {
            case client_id, first_name, last_name -> businessCustomerMapper.sortLimitCustomerIdByFilter(
                    clientsFilterDTO);
            case total_paid -> getTotalPayPage(
                    migrated, companyId, businessId, preferredBusinessIds, customerIds, clientListParams, sort);
            case client_total_appt_cnt -> getApptCountPage(
                    migrated,
                    companyId,
                    businessId,
                    preferredBusinessIds,
                    customerIds,
                    clientListParams,
                    businessDateTime,
                    sort);
            case upcoming_appt_date -> getUpcomingApptDataPage(
                    migrated,
                    companyId,
                    businessId,
                    preferredBusinessIds,
                    customerIds,
                    clientListParams,
                    businessDateTime,
                    sort);
            case last_appt_date -> getLastApptDataPage(
                    migrated,
                    companyId,
                    businessId,
                    preferredBusinessIds,
                    customerIds,
                    clientListParams,
                    businessDateTime,
                    sort);
            case expected_service_date -> getExpectedServiceDataPage(
                    migrated,
                    companyId,
                    businessId,
                    preferredBusinessIds,
                    customerIds,
                    clientListParams,
                    businessDateTime,
                    sort);
            case overdue -> getOverduePage(
                    migrated,
                    companyId,
                    businessId,
                    preferredBusinessIds,
                    customerIds,
                    clientListParams,
                    businessDateTime,
                    sort);
            default -> Collections.emptyList();
        };
    }

    private List<Integer> getOverduePage(
            boolean migrated,
            long companyId,
            Integer businessId,
            Set<Integer> preferredBusinessIds,
            Set<Integer> customerIds,
            ClientListParams clientListParams,
            BusinessDateTimeDTO businessDateTime,
            SortParams sort) {
        Map<Integer, Integer> customerToPreferredFrequencyDay =
                customerService.batchGetCustomerPreferredFrequencyDay(List.copyOf(customerIds));

        Comparator<Integer> cmp = sort.order() == PageQuery.OrderEnum.asc
                ? Comparator.naturalOrder()
                : Comparator.<Integer>naturalOrder().reversed();
        Comparator<CustomerApptDateDTO> comparator = Comparator.<CustomerApptDateDTO, Integer>comparing(
                        dto -> CustomerInfoConverter.getOverdue(
                                getExpectedService(
                                        dto.getLastApptDate(),
                                        customerToPreferredFrequencyDay.getOrDefault(dto.getCustomerId(), 0)),
                                businessDateTime.getLocalDateTime().toLocalDate()),
                        Comparator.nullsLast(cmp))
                .thenComparing(CustomerApptDateDTO::getCustomerId);

        // TODO: check company result
        List<CustomerApptDateDTO> dtos = new ArrayList<>(customerAppointmentService
                .listCustomerApptDate(new BusinessDateClientsDTO(
                        new BusinessClientsDTO(migrated, companyId, businessId, preferredBusinessIds, customerIds),
                        businessDateTime))
                .values());

        Set<Integer> ids = dtos.stream().map(CustomerApptDateDTO::getCustomerId).collect(Collectors.toSet());
        customerIds.stream()
                .filter(id -> !ids.contains(id))
                .forEach(id -> dtos.add(new CustomerApptDateDTO(id, null, null)));

        return dtos.stream()
                .sorted(comparator)
                .skip((long) (clientListParams.pageNum() - 1) * clientListParams.pageSize())
                .limit(clientListParams.pageSize())
                .map(CustomerApptDateDTO::getCustomerId)
                .toList();
    }

    private List<Integer> getExpectedServiceDataPage(
            boolean migrated,
            long companyId,
            Integer businessId,
            Set<Integer> preferredBusinessIds,
            Set<Integer> customerIds,
            ClientListParams clientListParams,
            BusinessDateTimeDTO businessDateTime,
            SortParams sort) {
        Map<Integer, Integer> customerToPreferredFrequencyDay =
                customerService.batchGetCustomerPreferredFrequencyDay(List.copyOf(customerIds));

        Comparator<CustomerApptDateDTO> comparator = Comparator.comparing(
                dto -> getExpectedService(
                        dto.getLastApptDate(), customerToPreferredFrequencyDay.getOrDefault(dto.getCustomerId(), 0)),
                Comparator.nullsFirst(Comparator.naturalOrder()));

        // TODO: check company result
        List<CustomerApptDateDTO> dtos = new ArrayList<>(customerAppointmentService
                .listCustomerApptDate(new BusinessDateClientsDTO(
                        new BusinessClientsDTO(migrated, companyId, businessId, preferredBusinessIds, customerIds),
                        businessDateTime))
                .values());

        Set<Integer> ids = dtos.stream().map(CustomerApptDateDTO::getCustomerId).collect(Collectors.toSet());
        customerIds.stream()
                .filter(id -> !ids.contains(id))
                .forEach(id -> dtos.add(new CustomerApptDateDTO(id, null, null)));

        return dtos.stream()
                .sorted(
                        sort.order() == PageQuery.OrderEnum.asc
                                ? comparator.thenComparing(CustomerApptDateDTO::getCustomerId)
                                : comparator
                                        .thenComparing(CustomerApptDateDTO::getCustomerId)
                                        .reversed())
                .skip((long) (clientListParams.pageNum() - 1) * clientListParams.pageSize())
                .limit(clientListParams.pageSize())
                .map(CustomerApptDateDTO::getCustomerId)
                .toList();
    }

    private List<Integer> getLastApptDataPage(
            boolean migrated,
            long companyId,
            Integer businessId,
            Set<Integer> preferredBusinessIds,
            Set<Integer> customerIds,
            ClientListParams clientListParams,
            BusinessDateTimeDTO businessDateTime,
            SortParams sort) {
        // TODO: check company result
        ArrayList<CustomerApptDateDTO> dtos = new ArrayList<>(customerAppointmentService
                .listCustomerApptDate(new BusinessDateClientsDTO(
                        new BusinessClientsDTO(migrated, companyId, businessId, preferredBusinessIds, customerIds),
                        businessDateTime))
                .values());

        Set<Integer> ids = dtos.stream().map(CustomerApptDateDTO::getCustomerId).collect(Collectors.toSet());
        customerIds.stream()
                .filter(id -> !ids.contains(id))
                .forEach(id -> dtos.add(new CustomerApptDateDTO(id, null, null)));

        Comparator<CustomerApptDateDTO> comparator = Comparator.comparing(
                        CustomerApptDateDTO::getLastApptDate, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(CustomerApptDateDTO::getCustomerId);

        return dtos.stream()
                .sorted(sort.order() == PageQuery.OrderEnum.asc ? comparator : comparator.reversed())
                .skip((long) (clientListParams.pageNum() - 1) * clientListParams.pageSize())
                .limit(clientListParams.pageSize())
                .map(CustomerApptDateDTO::getCustomerId)
                .toList();
    }

    private List<Integer> getUpcomingApptDataPage(
            boolean migrated,
            long companyId,
            Integer businessId,
            Set<Integer> preferredBusinessIds,
            Set<Integer> customerIds,
            ClientListParams clientListParams,
            BusinessDateTimeDTO businessDateTime,
            SortParams sort) {
        // TODO: check company result
        ArrayList<CustomerApptDateDTO> dtos = new ArrayList<>(customerAppointmentService
                .listCustomerApptDate(new BusinessDateClientsDTO(
                        new BusinessClientsDTO(migrated, companyId, businessId, preferredBusinessIds, customerIds),
                        businessDateTime))
                .values());

        Set<Integer> ids = dtos.stream().map(CustomerApptDateDTO::getCustomerId).collect(Collectors.toSet());
        customerIds.stream()
                .filter(id -> !ids.contains(id))
                .forEach(id -> dtos.add(new CustomerApptDateDTO(id, null, null)));

        Comparator<CustomerApptDateDTO> comparator = Comparator.comparing(
                        CustomerApptDateDTO::getNextApptDate, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(CustomerApptDateDTO::getCustomerId);

        return dtos.stream()
                .sorted(sort.order() == PageQuery.OrderEnum.asc ? comparator : comparator.reversed())
                .skip((long) (clientListParams.pageNum() - 1) * clientListParams.pageSize())
                .limit(clientListParams.pageSize())
                .map(CustomerApptDateDTO::getCustomerId)
                .toList();
    }

    private List<Integer> getApptCountPage(
            boolean migrated,
            long companyId,
            Integer businessId,
            Set<Integer> preferredBusinessIds,
            Set<Integer> customerIds,
            ClientListParams clientListParams,
            BusinessDateTimeDTO businessDateTime,
            SortParams sort) {
        // TODO: check company result
        ArrayList<CustomerApptCountDTO> dtos = new ArrayList<>(customerAppointmentService
                .listCustomerApptCount(new BusinessDateClientsDTO(
                        new BusinessClientsDTO(migrated, companyId, businessId, preferredBusinessIds, customerIds),
                        businessDateTime))
                .values());

        Set<Integer> ids =
                dtos.stream().map(CustomerApptCountDTO::getCustomerId).collect(Collectors.toSet());
        customerIds.stream()
                .filter(id -> !ids.contains(id))
                .forEach(id -> dtos.add(new CustomerApptCountDTO(id, 0, 0, 0)));

        Comparator<CustomerApptCountDTO> comparator = Comparator.comparing(CustomerApptCountDTO::getTotalApptCount)
                .thenComparing(CustomerApptCountDTO::getCustomerId);
        return dtos.stream()
                .sorted(sort.order() == PageQuery.OrderEnum.asc ? comparator : comparator.reversed())
                .skip((long) (clientListParams.pageNum() - 1) * clientListParams.pageSize())
                .limit(clientListParams.pageSize())
                .map(CustomerApptCountDTO::getCustomerId)
                .toList();
    }

    private List<Integer> getTotalPayPage(
            boolean migrated,
            long companyId,
            Integer businessId,
            Set<Integer> preferredBusinessIds,
            Set<Integer> customerIds,
            ClientListParams clientListParams,
            SortParams sort) {
        List<CustomerPaymentDTO> dtos = new ArrayList<>(customerPaymentService
                .listCustomerPayment(
                        new BusinessClientsDTO(migrated, companyId, businessId, preferredBusinessIds, customerIds))
                .values());

        Set<Integer> ids = dtos.stream().map(CustomerPaymentDTO::getCustomerId).collect(Collectors.toSet());
        customerIds.stream()
                .filter(id -> !ids.contains(id))
                .forEach(id -> dtos.add(new CustomerPaymentDTO(id, BigDecimal.ZERO)));

        Comparator<CustomerPaymentDTO> comparator = Comparator.comparing(CustomerPaymentDTO::getTotalPaidAmount)
                .thenComparing(CustomerPaymentDTO::getCustomerId);
        return dtos.stream()
                .sorted(sort.order() == PageQuery.OrderEnum.asc ? comparator : comparator.reversed())
                .skip((long) (clientListParams.pageNum() - 1) * clientListParams.pageSize())
                .limit(clientListParams.pageSize())
                .map(CustomerPaymentDTO::getCustomerId)
                .toList();
    }

    public CustomerFilterResult listFilterClientContactPageable(
            boolean migrated,
            long companyId,
            Integer businessId,
            Set<Integer> preferredBusinessIds,
            ClientListParams clientListParams) {
        CustomerFilterResult customerFilterResult = new CustomerFilterResult();
        // Get business time zone for date filter
        BusinessDateTimeDTO dateTime =
                migrated ? getCompanyTimeZone(companyId) : getBusinessTimeZone(companyId, businessId);
        Set<Integer> customerIds = listAllCustomerIdByCustomProperty(
                companyId, businessId, preferredBusinessIds, clientListParams, dateTime);
        if (CollectionUtils.isEmpty(customerIds)) {
            customerFilterResult.setCustomerTotal(0);
            customerFilterResult.setPhoneNumberMap(Collections.emptyMap());
            customerFilterResult.setCustomerList(List.of());
            return customerFilterResult;
        }
        // Sort and pagination
        List<Integer> sortedCustomerIds = sortByAndPagination(
                migrated, companyId, businessId, preferredBusinessIds, customerIds, clientListParams, dateTime);
        List<ClientListVO> sortedCustomers =
                listClientListInfo(companyId, businessId, preferredBusinessIds, sortedCustomerIds, dateTime);
        if (CollectionUtils.isEmpty(sortedCustomers)) {
            customerFilterResult.setCustomerTotal(0);
            customerFilterResult.setPhoneNumberMap(Collections.emptyMap());
            customerFilterResult.setCustomerList(List.of());
            return customerFilterResult;
        }
        Map<Integer, PhoneNumberEmailDto> phoneNumberMap = new HashMap<>();
        List<PhoneNumberEmailDto> customerResult = new ArrayList<>();
        for (ClientListVO customer : sortedCustomers) {
            var customerDTO = CustomerConverter.INSTANCE.clientListVo2PhoneNumberEmailDto(customer);
            customerResult.add(customerDTO);
            phoneNumberMap.put(customer.customerId(), customerDTO);
        }
        customerFilterResult.setPhoneNumberMap(phoneNumberMap);
        customerFilterResult.setCustomerTotal(customerIds.size());
        customerFilterResult.setCustomerList(customerResult);
        return customerFilterResult;
    }

    /**
     * List customer id by bulk params
     * Filters contains the filter of positive and negative selection customer id
     *
     * @param businessId           business id
     * @param clientListBulkParams bulk params, contains include customer id, exclude customer id, custom property filter
     * @return bulk customer id set
     */
    public Set<Integer> listCustomerIdByBulkParams(
            boolean migrated,
            long companyId,
            Integer businessId,
            long staffId,
            ClientListBulkParams clientListBulkParams) {

        if (!CollectionUtils.isEmpty(clientListBulkParams.includeCustomerIds())) {
            return clientListBulkParams.includeCustomerIds();
        }
        // Filter customer id by custom property
        ClientListParams clientListParams = ClientListParams.builder()
                .filters(clientListBulkParams.filters())
                .queries(clientListBulkParams.queries())
                .build();

        Set<Integer> preferredBusinessIds = null;
        if (migrated) {
            var extract = CustomerFilterConverter.extractPreferredBusinessFilter(clientListParams);
            clientListParams = extract.clientListParams();
            preferredBusinessIds =
                    permissionHelper.fillWorkingLocationIds(companyId, staffId, extract.preferredBusinessIds());
        }

        // Get business time zone for date filter
        BusinessDateTimeDTO dateTime =
                migrated ? getCompanyTimeZone(companyId) : getBusinessTimeZone(companyId, businessId);
        Set<Integer> customerIds = listAllCustomerIdByCustomProperty(
                companyId, businessId, preferredBusinessIds, clientListParams, dateTime);
        // Exclude customer id
        if (!CollectionUtils.isEmpty(clientListBulkParams.excludeCustomerIds())) {
            customerIds.removeAll(clientListBulkParams.excludeCustomerIds());
        }
        return customerIds;
    }

    public int batchDeleteCustomer(
            long companyId, Integer businessId, long staffId, ClientListBulkParams clientListBulkParams) {
        Set<Integer> customerIds =
                listCustomerIdByBulkParams(true, companyId, businessId, staffId, clientListBulkParams);
        if (CollectionUtils.isEmpty(customerIds)) {
            return 0;
        }

        var merging = grpcCustomerService.hasMergingCustomer(
                customerIds.stream().map(Long::valueOf).toList());
        if (merging) {
            throw ExceptionUtil.bizException(
                    Code.CODE_CUSTOMER_MERGING, "Some clients are merging, please try again later.");
        }

        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        futureList.add(CompletableFuture.runAsync(
                () -> {
                    MoeBusinessCustomer customer = new MoeBusinessCustomer();
                    customer.setStatus(CommonConstant.DELETED);
                    customer.setUpdateTime(DateUtil.get10Timestamp());
                    customer.setUpdateBy((int) staffId);
                    businessCustomerMapper.batchUpdateCustomer(customerIds, customer);
                },
                executor));
        CompletableFuture.runAsync(
                () -> customerContactMapper.batchDeleteByCustomerId(customerIds, DateUtil.get10Timestamp()), executor);
        CompletableFuture.runAsync(
                () -> customerPetMapper.batchDeleteByCustomerId(customerIds, DateUtil.get10Timestamp()), executor);
        CompletableFuture.runAsync(
                () -> customerAppointmentService.batchDeleteCustomer(companyId, businessId, customerIds, (int) staffId),
                executor);
        CompletableFuture.runAsync(
                () -> customerMessageService.batchDeleteCustomerMessage(companyId, businessId, customerIds), executor);
        CompletableFuture.runAsync(() -> publishEvent(companyId, businessId, customerIds), executor);
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        ActivityLogRecorder.recordRoot(Action.BATCH_DELETE, ResourceType.CUSTOMER, null, clientListBulkParams);
        customerIds.forEach(
                customerId -> ActivityLogRecorder.record(Action.DELETE, ResourceType.CUSTOMER, customerId, null));
        return customerIds.size();
    }

    private void publishEvent(long companyId, Integer businessId, Set<Integer> customerIds) {
        customerIds.forEach(customerId -> activeMQService.publishEvent(new CustomerEventParams()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setCustomerId(customerId)
                .setEvent(CustomerEventParams.CustomerEvent.DELETED)));
    }

    public int batchUpdateInactive(
            boolean migrated,
            long companyId,
            Integer businessId,
            long staffId,
            ClientListBulkParams clientListBulkParams) {
        Set<Integer> customerIds =
                listCustomerIdByBulkParams(migrated, companyId, businessId, staffId, clientListBulkParams);
        if (CollectionUtils.isEmpty(customerIds)) {
            return 0;
        }
        MoeBusinessCustomer customer = new MoeBusinessCustomer();
        customer.setInactive(clientListBulkParams.enable());
        customer.setUpdateTime(DateUtil.get10Timestamp());
        customer.setUpdateBy((int) staffId);
        return businessCustomerMapper.batchUpdateCustomer(customerIds, customer);
    }

    public int batchAddTag(
            boolean migrated,
            long companyId,
            Integer businessId,
            long staffId,
            ClientListBulkParams clientListBulkParams) {
        Set<Integer> customerIds =
                listCustomerIdByBulkParams(migrated, companyId, businessId, staffId, clientListBulkParams);
        if (CollectionUtils.isEmpty(customerIds)) {
            return 0;
        }
        List<MoeCustomerTagBinding> customerTagBindingList = new ArrayList<>();
        customerIds.forEach(customerId -> {
            Set<Integer> relationIds = clientListBulkParams.relationIds();
            relationIds.forEach(relationId -> {
                MoeCustomerTagBinding customerTagBinding = new MoeCustomerTagBinding();
                customerTagBinding.setCustomerId(customerId);
                customerTagBinding.setCustomerTagId(relationId);
                customerTagBindingList.add(customerTagBinding);
            });
        });
        return customerTagBindingMapper.batchInsert(customerTagBindingList);
    }

    static String getExpectedService(String lastApptDate, int preferredFrequencyDay) {
        if (!StringUtils.hasText(lastApptDate)) {
            return null;
        }
        return LocalDate.parse(lastApptDate).plusDays(preferredFrequencyDay).toString();
    }

    static ClientFilterVO toClientFilterVO(MoeCustomerFilter entity) {
        var builder = ClientFilterVO.builder();
        builder.id(entity.getId());
        builder.companyId(entity.getCompanyId());
        builder.staffId(entity.getStaffId());
        builder.title(entity.getTitle());
        builder.isDefault(entity.getIsDefault());
        if (StringUtils.hasText(entity.getFields())) {
            builder.fields(Arrays.asList(entity.getFields().split(",")));
        } else {
            builder.fields(Collections.emptyList());
        }
        if (StringUtils.hasText(entity.getOrderBy())) {
            builder.orderBy(JsonUtil.toBean(entity.getOrderBy(), SortParams.class));
        }
        if (StringUtils.hasText(entity.getFilter())) {
            builder.filter(JsonUtil.toBean(entity.getFilter(), FilterParams.class));
        }

        builder.createdAt(
                entity.getCreatedAt() == null
                        ? LocalDateTime.now()
                        : entity.getCreatedAt()
                                .toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime());
        builder.updatedAt(
                entity.getUpdatedAt() == null
                        ? LocalDateTime.now()
                        : entity.getUpdatedAt()
                                .toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime());

        return builder.build();
    }

    static MoeCustomerFilter toMoeCustomerFilter(ClientFilterVO vo) {
        var entity = new MoeCustomerFilter();
        entity.setId(vo.id());
        entity.setCompanyId(vo.companyId());
        entity.setStaffId(vo.staffId());
        entity.setIsDefault(vo.isDefault() != null && vo.isDefault());
        entity.setTitle(vo.title());
        if (vo.fields() != null && !vo.fields().isEmpty()) {
            entity.setFields(String.join(",", vo.fields()));
        }
        if (vo.orderBy() != null) {
            entity.setOrderBy(JsonUtil.toJson(vo.orderBy()));
        }
        if (vo.filter() != null) {
            entity.setFilter(JsonUtil.toJson(vo.filter()));
        }
        if (vo.createdAt() != null) {
            entity.setCreatedAt(
                    Date.from(vo.createdAt().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (vo.updatedAt() != null) {
            entity.setUpdatedAt(
                    Date.from(vo.updatedAt().atZone(ZoneId.systemDefault()).toInstant()));
        }

        return entity;
    }

    static ClientFilterVO buildAllClientsView(long companyId, long staffId) {
        return ClientFilterVO.builder()
                .companyId(companyId)
                .staffId(staffId)
                .title("All clients")
                .isDefault(true)
                .orderBy(new SortParams(PropertyEnum.first_name, PageQuery.OrderEnum.asc))
                .filter(FilterParams.builder().filters(Collections.emptyList()).build())
                .build();
    }

    static ClientFilterVO buildNeedRebookClientView(long companyId, long staffId) {
        return ClientFilterVO.builder()
                .companyId(companyId)
                .staffId(staffId)
                .title("Need to rebook")
                .isDefault(false)
                .fields(List.of("clientName", "pets", "contact", "lastService"))
                .orderBy(new SortParams(PropertyEnum.first_name, PageQuery.OrderEnum.asc))
                .filter(FilterParams.builder()
                        .type(TypeEnum.TYPE_AND)
                        .filters(List.of(lapsedClientsFilter, lastApptLess90DaysFilter, noUpcomingApptFilter))
                        .build())
                .build();
    }

    static ClientFilterVO buildUnpaidClientView(long companyId, long staffId) {
        return ClientFilterVO.builder()
                .companyId(companyId)
                .staffId(staffId)
                .title("Unpaid invoice")
                .isDefault(false)
                .fields(List.of("clientName", "pets", "contact", "lastService"))
                .orderBy(new SortParams(PropertyEnum.last_appt_date, PageQuery.OrderEnum.desc))
                .filter(FilterParams.builder()
                        .type(TypeEnum.TYPE_AND)
                        .filters(List.of(activeClientsFilter, unpaidClientsFilter))
                        .build())
                .build();
    }

    static ClientFilterVO buildThisWeekClientView(long companyId, long staffId) {
        return ClientFilterVO.builder()
                .companyId(companyId)
                .staffId(staffId)
                .title("In 7 days")
                .isDefault(false)
                .fields(List.of("clientName", "pets", "contact", "upcomingBooking", "lastService"))
                .orderBy(new SortParams(PropertyEnum.upcoming_appt_date, PageQuery.OrderEnum.asc))
                .filter(FilterParams.builder()
                        .type(TypeEnum.TYPE_AND)
                        .filters(List.of(activeClientsFilter, nextApptLess7DaysFilter))
                        .build())
                .build();
    }

    static FilterParams activeClientsFilter = FilterParams.builder()
            .property(PropertyEnum.client_status)
            .operator(OperatorEnum.OPERATOR_EQUAL)
            .value("active")
            .build();

    static FilterParams lapsedClientsFilter = FilterParams.builder()
            .property(PropertyEnum.client_status)
            .operator(OperatorEnum.OPERATOR_EQUAL)
            .value("lapsed")
            .build();

    static FilterParams unpaidClientsFilter = FilterParams.builder()
            .property(PropertyEnum.unpaid_invoice_cnt)
            .operator(OperatorEnum.OPERATOR_GREATER_THAN)
            .value("0")
            .build();

    static FilterParams lastApptLess90DaysFilter = FilterParams.builder()
            .property(PropertyEnum.last_appt_date)
            .operator(OperatorEnum.OPERATOR_GREATER_THAN)
            .value("90")
            .build();

    static FilterParams nextApptLess7DaysFilter = FilterParams.builder()
            .property(PropertyEnum.next_appt_date)
            .operator(OperatorEnum.OPERATOR_LESS_THAN)
            .value("7")
            .build();

    static FilterParams noUpcomingApptFilter = FilterParams.builder()
            .property(PropertyEnum.upcoming_appt_cnt)
            .operator(OperatorEnum.OPERATOR_EQUAL)
            .value("0")
            .build();
}
