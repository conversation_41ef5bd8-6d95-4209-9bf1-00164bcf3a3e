orderDsn: postgresql://moego_developer_240310_eff7a0dc:<EMAIL>:40132/moego_order?sslmode=disable
paymentDsn: moego_developer_240310_eff7a0dc:G0MxI7NM_jX_f7Ky73vnrwej97xg1tly@tcp(mysql.t2.moego.dev:40106)/moe_payment?parseTime=true
eventBus:
  brokers:
    - kafka.kafka.svc.cluster.local:9092
  consumers:
    payment:
      topic: moego.payment
      groupID: order
    appointment:
      topic: moego.erp.appointment
      groupID: order
    fulfillment:
      topic: moego.fulfillment
      groupID: order
    order:
      topic: moego.order
      groupID: order
    orderTips:
      topic: moego.order
      groupID: order_tips
redis:
  addr: redis.t2.moego.dev:40179
  password: iMoReGoTdesstingeCache250310_7fec987d
  tls: true
  insecureSkipVerify: true
growthBook:
  host: https://growthbook.moego.pet/growthbook-api
  clientKey: sdk-QF3sU88BEtcqVhr
  interval: 10s