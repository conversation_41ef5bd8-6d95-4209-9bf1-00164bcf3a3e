// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment (interfaces: Repository)
//
// Generated by this command:
//
//	mockgen -package=fulfillment -destination=mocks/mock_fulfillment_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment Repository
//

// Package fulfillment is a generated GoMock package.
package fulfillment

import (
	context "context"
	reflect "reflect"

	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	fulfillment "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment"
	filter "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	model "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	query "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
	dto "github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRepository) Create(ctx context.Context, arg1 *model.Fulfillment) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), ctx, arg1)
}

// GetByID mocks base method.
func (m *MockRepository) GetByID(ctx context.Context, id int64) (*model.Fulfillment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.Fulfillment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockRepositoryMockRecorder) GetByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockRepository)(nil).GetByID), ctx, id)
}

// List mocks base method.
func (m *MockRepository) List(ctx context.Context, arg1 *filter.ListFulfillmentFilter, pagination *utilsV2.PaginationRequest) ([]*model.Fulfillment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, arg1, pagination)
	ret0, _ := ret[0].([]*model.Fulfillment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockRepositoryMockRecorder) List(ctx, arg1, pagination any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockRepository)(nil).List), ctx, arg1, pagination)
}

// Update mocks base method.
func (m *MockRepository) Update(ctx context.Context, arg1 *model.Fulfillment) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockRepositoryMockRecorder) Update(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRepository)(nil).Update), ctx, arg1)
}

// UpdateSelective mocks base method.
func (m *MockRepository) UpdateSelective(ctx context.Context, update dto.UpdateFulfillmentDTO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSelective", ctx, update)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSelective indicates an expected call of UpdateSelective.
func (mr *MockRepositoryMockRecorder) UpdateSelective(ctx, update any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSelective", reflect.TypeOf((*MockRepository)(nil).UpdateSelective), ctx, update)
}

// WithQuery mocks base method.
func (m *MockRepository) WithQuery(q *query.Query) fulfillment.Repository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithQuery", q)
	ret0, _ := ret[0].(fulfillment.Repository)
	return ret0
}

// WithQuery indicates an expected call of WithQuery.
func (mr *MockRepositoryMockRecorder) WithQuery(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithQuery", reflect.TypeOf((*MockRepository)(nil).WithQuery), q)
}
