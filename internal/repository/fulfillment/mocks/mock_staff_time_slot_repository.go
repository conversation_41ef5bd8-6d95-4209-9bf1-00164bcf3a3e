// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment (interfaces: StaffTimeSlotRepository)
//
// Generated by this command:
//
//	mockgen -package=fulfillment -destination=mocks/mock_staff_time_slot_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment StaffTimeSlotRepository
//

// Package fulfillment is a generated GoMock package.
package fulfillment

import (
	context "context"
	reflect "reflect"

	fulfillment "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment"
	filter "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	model "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	query "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
	dto "github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockStaffTimeSlotRepository is a mock of StaffTimeSlotRepository interface.
type MockStaffTimeSlotRepository struct {
	ctrl     *gomock.Controller
	recorder *MockStaffTimeSlotRepositoryMockRecorder
	isgomock struct{}
}

// MockStaffTimeSlotRepositoryMockRecorder is the mock recorder for MockStaffTimeSlotRepository.
type MockStaffTimeSlotRepositoryMockRecorder struct {
	mock *MockStaffTimeSlotRepository
}

// NewMockStaffTimeSlotRepository creates a new mock instance.
func NewMockStaffTimeSlotRepository(ctrl *gomock.Controller) *MockStaffTimeSlotRepository {
	mock := &MockStaffTimeSlotRepository{ctrl: ctrl}
	mock.recorder = &MockStaffTimeSlotRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStaffTimeSlotRepository) EXPECT() *MockStaffTimeSlotRepositoryMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockStaffTimeSlotRepository) BatchCreate(ctx context.Context, models []*model.StaffTimeSlot) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, models)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockStaffTimeSlotRepositoryMockRecorder) BatchCreate(ctx, models any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockStaffTimeSlotRepository)(nil).BatchCreate), ctx, models)
}

// BatchUpdate mocks base method.
func (m *MockStaffTimeSlotRepository) BatchUpdate(ctx context.Context, models []*model.StaffTimeSlot) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdate", ctx, models)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdate indicates an expected call of BatchUpdate.
func (mr *MockStaffTimeSlotRepositoryMockRecorder) BatchUpdate(ctx, models any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdate", reflect.TypeOf((*MockStaffTimeSlotRepository)(nil).BatchUpdate), ctx, models)
}

// BatchUpdateSelective mocks base method.
func (m *MockStaffTimeSlotRepository) BatchUpdateSelective(ctx context.Context, updates []dto.UpdateStaffTimeSlotDTO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateSelective", ctx, updates)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateSelective indicates an expected call of BatchUpdateSelective.
func (mr *MockStaffTimeSlotRepositoryMockRecorder) BatchUpdateSelective(ctx, updates any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateSelective", reflect.TypeOf((*MockStaffTimeSlotRepository)(nil).BatchUpdateSelective), ctx, updates)
}

// DeleteByFulfillmentID mocks base method.
func (m *MockStaffTimeSlotRepository) DeleteByFulfillmentID(ctx context.Context, fulfillmentID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByFulfillmentID", ctx, fulfillmentID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByFulfillmentID indicates an expected call of DeleteByFulfillmentID.
func (mr *MockStaffTimeSlotRepositoryMockRecorder) DeleteByFulfillmentID(ctx, fulfillmentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByFulfillmentID", reflect.TypeOf((*MockStaffTimeSlotRepository)(nil).DeleteByFulfillmentID), ctx, fulfillmentID)
}

// ListByFilter mocks base method.
func (m *MockStaffTimeSlotRepository) ListByFilter(ctx context.Context, arg1 filter.ListStaffTimeSlotFilter) ([]*model.StaffTimeSlot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByFilter", ctx, arg1)
	ret0, _ := ret[0].([]*model.StaffTimeSlot)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByFilter indicates an expected call of ListByFilter.
func (mr *MockStaffTimeSlotRepositoryMockRecorder) ListByFilter(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByFilter", reflect.TypeOf((*MockStaffTimeSlotRepository)(nil).ListByFilter), ctx, arg1)
}

// WithQuery mocks base method.
func (m *MockStaffTimeSlotRepository) WithQuery(q *query.Query) fulfillment.StaffTimeSlotRepository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithQuery", q)
	ret0, _ := ret[0].(fulfillment.StaffTimeSlotRepository)
	return ret0
}

// WithQuery indicates an expected call of WithQuery.
func (mr *MockStaffTimeSlotRepositoryMockRecorder) WithQuery(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithQuery", reflect.TypeOf((*MockStaffTimeSlotRepository)(nil).WithQuery), q)
}
