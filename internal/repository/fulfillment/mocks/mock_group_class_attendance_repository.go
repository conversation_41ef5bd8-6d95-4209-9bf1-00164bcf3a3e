// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment (interfaces: GroupClassAttendanceRepository)
//
// Generated by this command:
//
//	mockgen -package=fulfillment -destination=mocks/mock_group_class_attendance_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment GroupClassAttendanceRepository
//

// Package fulfillment is a generated GoMock package.
package fulfillment

import (
	context "context"
	reflect "reflect"

	fulfillment "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment"
	filter "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	model "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	query "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
	gomock "go.uber.org/mock/gomock"
)

// MockGroupClassAttendanceRepository is a mock of GroupClassAttendanceRepository interface.
type MockGroupClassAttendanceRepository struct {
	ctrl     *gomock.Controller
	recorder *MockGroupClassAttendanceRepositoryMockRecorder
	isgomock struct{}
}

// MockGroupClassAttendanceRepositoryMockRecorder is the mock recorder for MockGroupClassAttendanceRepository.
type MockGroupClassAttendanceRepositoryMockRecorder struct {
	mock *MockGroupClassAttendanceRepository
}

// NewMockGroupClassAttendanceRepository creates a new mock instance.
func NewMockGroupClassAttendanceRepository(ctrl *gomock.Controller) *MockGroupClassAttendanceRepository {
	mock := &MockGroupClassAttendanceRepository{ctrl: ctrl}
	mock.recorder = &MockGroupClassAttendanceRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGroupClassAttendanceRepository) EXPECT() *MockGroupClassAttendanceRepositoryMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockGroupClassAttendanceRepository) BatchCreate(ctx context.Context, arg1 []*model.GroupClassAttendance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockGroupClassAttendanceRepositoryMockRecorder) BatchCreate(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockGroupClassAttendanceRepository)(nil).BatchCreate), ctx, arg1)
}

// Create mocks base method.
func (m *MockGroupClassAttendanceRepository) Create(ctx context.Context, arg1 *model.GroupClassAttendance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockGroupClassAttendanceRepositoryMockRecorder) Create(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockGroupClassAttendanceRepository)(nil).Create), ctx, arg1)
}

// DeleteByFulfillmentID mocks base method.
func (m *MockGroupClassAttendanceRepository) DeleteByFulfillmentID(ctx context.Context, fulfillmentID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByFulfillmentID", ctx, fulfillmentID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByFulfillmentID indicates an expected call of DeleteByFulfillmentID.
func (mr *MockGroupClassAttendanceRepositoryMockRecorder) DeleteByFulfillmentID(ctx, fulfillmentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByFulfillmentID", reflect.TypeOf((*MockGroupClassAttendanceRepository)(nil).DeleteByFulfillmentID), ctx, fulfillmentID)
}

// ListByFilter mocks base method.
func (m *MockGroupClassAttendanceRepository) ListByFilter(ctx context.Context, arg1 filter.ListGroupClassAttendanceFilter) ([]*model.GroupClassAttendance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByFilter", ctx, arg1)
	ret0, _ := ret[0].([]*model.GroupClassAttendance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByFilter indicates an expected call of ListByFilter.
func (mr *MockGroupClassAttendanceRepositoryMockRecorder) ListByFilter(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByFilter", reflect.TypeOf((*MockGroupClassAttendanceRepository)(nil).ListByFilter), ctx, arg1)
}

// WithQuery mocks base method.
func (m *MockGroupClassAttendanceRepository) WithQuery(q *query.Query) fulfillment.GroupClassAttendanceRepository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithQuery", q)
	ret0, _ := ret[0].(fulfillment.GroupClassAttendanceRepository)
	return ret0
}

// WithQuery indicates an expected call of WithQuery.
func (mr *MockGroupClassAttendanceRepositoryMockRecorder) WithQuery(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithQuery", reflect.TypeOf((*MockGroupClassAttendanceRepository)(nil).WithQuery), q)
}
