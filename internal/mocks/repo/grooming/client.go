// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	grooming "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
	decimal "github.com/shopspring/decimal"

	mock "github.com/stretchr/testify/mock"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

// Client is an autogenerated mock type for the Client type
type Client struct {
	mock.Mock
}

type Client_Expecter struct {
	mock *mock.Mock
}

func (_m *Client) EXPECT() *Client_Expecter {
	return &Client_Expecter{mock: &_m.Mock}
}

// GetTipsSplitDetails provides a mock function with given fields: ctx, orderIDs
func (_m *Client) GetTipsSplitDetails(ctx context.Context, orderIDs []int64) (*grooming.TipsSplitDetailsDTO, error) {
	ret := _m.Called(ctx, orderIDs)

	if len(ret) == 0 {
		panic("no return value specified for GetTipsSplitDetails")
	}

	var r0 *grooming.TipsSplitDetailsDTO
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) (*grooming.TipsSplitDetailsDTO, error)); ok {
		return rf(ctx, orderIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) *grooming.TipsSplitDetailsDTO); ok {
		r0 = rf(ctx, orderIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*grooming.TipsSplitDetailsDTO)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, orderIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_GetTipsSplitDetails_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTipsSplitDetails'
type Client_GetTipsSplitDetails_Call struct {
	*mock.Call
}

// GetTipsSplitDetails is a helper method to define mock.On call
//   - ctx context.Context
//   - orderIDs []int64
func (_e *Client_Expecter) GetTipsSplitDetails(ctx interface{}, orderIDs interface{}) *Client_GetTipsSplitDetails_Call {
	return &Client_GetTipsSplitDetails_Call{Call: _e.mock.On("GetTipsSplitDetails", ctx, orderIDs)}
}

func (_c *Client_GetTipsSplitDetails_Call) Run(run func(ctx context.Context, orderIDs []int64)) *Client_GetTipsSplitDetails_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *Client_GetTipsSplitDetails_Call) Return(_a0 *grooming.TipsSplitDetailsDTO, _a1 error) *Client_GetTipsSplitDetails_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_GetTipsSplitDetails_Call) RunAndReturn(run func(context.Context, []int64) (*grooming.TipsSplitDetailsDTO, error)) *Client_GetTipsSplitDetails_Call {
	_c.Call.Return(run)
	return _c
}

// GetTipsSplitDetailsMap provides a mock function with given fields: ctx, orderIDs, currencyCode
func (_m *Client) GetTipsSplitDetailsMap(ctx context.Context, orderIDs []int64, currencyCode string) (map[int64]*orderpb.StaffTipConfig, decimal.Decimal, error) {
	ret := _m.Called(ctx, orderIDs, currencyCode)

	if len(ret) == 0 {
		panic("no return value specified for GetTipsSplitDetailsMap")
	}

	var r0 map[int64]*orderpb.StaffTipConfig
	var r1 decimal.Decimal
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64, string) (map[int64]*orderpb.StaffTipConfig, decimal.Decimal, error)); ok {
		return rf(ctx, orderIDs, currencyCode)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64, string) map[int64]*orderpb.StaffTipConfig); ok {
		r0 = rf(ctx, orderIDs, currencyCode)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[int64]*orderpb.StaffTipConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64, string) decimal.Decimal); ok {
		r1 = rf(ctx, orderIDs, currencyCode)
	} else {
		r1 = ret.Get(1).(decimal.Decimal)
	}

	if rf, ok := ret.Get(2).(func(context.Context, []int64, string) error); ok {
		r2 = rf(ctx, orderIDs, currencyCode)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// Client_GetTipsSplitDetailsMap_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTipsSplitDetailsMap'
type Client_GetTipsSplitDetailsMap_Call struct {
	*mock.Call
}

// GetTipsSplitDetailsMap is a helper method to define mock.On call
//   - ctx context.Context
//   - orderIDs []int64
//   - currencyCode string
func (_e *Client_Expecter) GetTipsSplitDetailsMap(ctx interface{}, orderIDs interface{}, currencyCode interface{}) *Client_GetTipsSplitDetailsMap_Call {
	return &Client_GetTipsSplitDetailsMap_Call{Call: _e.mock.On("GetTipsSplitDetailsMap", ctx, orderIDs, currencyCode)}
}

func (_c *Client_GetTipsSplitDetailsMap_Call) Run(run func(ctx context.Context, orderIDs []int64, currencyCode string)) *Client_GetTipsSplitDetailsMap_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64), args[2].(string))
	})
	return _c
}

func (_c *Client_GetTipsSplitDetailsMap_Call) Return(_a0 map[int64]*orderpb.StaffTipConfig, _a1 decimal.Decimal, _a2 error) *Client_GetTipsSplitDetailsMap_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *Client_GetTipsSplitDetailsMap_Call) RunAndReturn(run func(context.Context, []int64, string) (map[int64]*orderpb.StaffTipConfig, decimal.Decimal, error)) *Client_GetTipsSplitDetailsMap_Call {
	_c.Call.Return(run)
	return _c
}

// ListOBSetting provides a mock function with given fields: ctx, businessIDs
func (_m *Client) ListOBSetting(ctx context.Context, businessIDs []int64) ([]*grooming.OBSetting, error) {
	ret := _m.Called(ctx, businessIDs)

	if len(ret) == 0 {
		panic("no return value specified for ListOBSetting")
	}

	var r0 []*grooming.OBSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*grooming.OBSetting, error)); ok {
		return rf(ctx, businessIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*grooming.OBSetting); ok {
		r0 = rf(ctx, businessIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*grooming.OBSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, businessIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_ListOBSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListOBSetting'
type Client_ListOBSetting_Call struct {
	*mock.Call
}

// ListOBSetting is a helper method to define mock.On call
//   - ctx context.Context
//   - businessIDs []int64
func (_e *Client_Expecter) ListOBSetting(ctx interface{}, businessIDs interface{}) *Client_ListOBSetting_Call {
	return &Client_ListOBSetting_Call{Call: _e.mock.On("ListOBSetting", ctx, businessIDs)}
}

func (_c *Client_ListOBSetting_Call) Run(run func(ctx context.Context, businessIDs []int64)) *Client_ListOBSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *Client_ListOBSetting_Call) Return(_a0 []*grooming.OBSetting, _a1 error) *Client_ListOBSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_ListOBSetting_Call) RunAndReturn(run func(context.Context, []int64) ([]*grooming.OBSetting, error)) *Client_ListOBSetting_Call {
	_c.Call.Return(run)
	return _c
}

// ListPetDetailsByGroomingID provides a mock function with given fields: ctx, businessID, groomingID
func (_m *Client) ListPetDetailsByGroomingID(ctx context.Context, businessID int64, groomingID int64) ([]*grooming.PetDetailDTO, error) {
	ret := _m.Called(ctx, businessID, groomingID)

	if len(ret) == 0 {
		panic("no return value specified for ListPetDetailsByGroomingID")
	}

	var r0 []*grooming.PetDetailDTO
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64) ([]*grooming.PetDetailDTO, error)); ok {
		return rf(ctx, businessID, groomingID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64) []*grooming.PetDetailDTO); ok {
		r0 = rf(ctx, businessID, groomingID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*grooming.PetDetailDTO)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, int64) error); ok {
		r1 = rf(ctx, businessID, groomingID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_ListPetDetailsByGroomingID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListPetDetailsByGroomingID'
type Client_ListPetDetailsByGroomingID_Call struct {
	*mock.Call
}

// ListPetDetailsByGroomingID is a helper method to define mock.On call
//   - ctx context.Context
//   - businessID int64
//   - groomingID int64
func (_e *Client_Expecter) ListPetDetailsByGroomingID(ctx interface{}, businessID interface{}, groomingID interface{}) *Client_ListPetDetailsByGroomingID_Call {
	return &Client_ListPetDetailsByGroomingID_Call{Call: _e.mock.On("ListPetDetailsByGroomingID", ctx, businessID, groomingID)}
}

func (_c *Client_ListPetDetailsByGroomingID_Call) Run(run func(ctx context.Context, businessID int64, groomingID int64)) *Client_ListPetDetailsByGroomingID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64))
	})
	return _c
}

func (_c *Client_ListPetDetailsByGroomingID_Call) Return(_a0 []*grooming.PetDetailDTO, _a1 error) *Client_ListPetDetailsByGroomingID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_ListPetDetailsByGroomingID_Call) RunAndReturn(run func(context.Context, int64, int64) ([]*grooming.PetDetailDTO, error)) *Client_ListPetDetailsByGroomingID_Call {
	_c.Call.Return(run)
	return _c
}

// ResetOBGroupPaymentSetting provides a mock function with given fields: ctx, businessID
func (_m *Client) ResetOBGroupPaymentSetting(ctx context.Context, businessID int64) error {
	ret := _m.Called(ctx, businessID)

	if len(ret) == 0 {
		panic("no return value specified for ResetOBGroupPaymentSetting")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, businessID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Client_ResetOBGroupPaymentSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResetOBGroupPaymentSetting'
type Client_ResetOBGroupPaymentSetting_Call struct {
	*mock.Call
}

// ResetOBGroupPaymentSetting is a helper method to define mock.On call
//   - ctx context.Context
//   - businessID int64
func (_e *Client_Expecter) ResetOBGroupPaymentSetting(ctx interface{}, businessID interface{}) *Client_ResetOBGroupPaymentSetting_Call {
	return &Client_ResetOBGroupPaymentSetting_Call{Call: _e.mock.On("ResetOBGroupPaymentSetting", ctx, businessID)}
}

func (_c *Client_ResetOBGroupPaymentSetting_Call) Run(run func(ctx context.Context, businessID int64)) *Client_ResetOBGroupPaymentSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Client_ResetOBGroupPaymentSetting_Call) Return(_a0 error) *Client_ResetOBGroupPaymentSetting_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_ResetOBGroupPaymentSetting_Call) RunAndReturn(run func(context.Context, int64) error) *Client_ResetOBGroupPaymentSetting_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOBPaymentSetting provides a mock function with given fields: ctx, businessID, update
func (_m *Client) UpdateOBPaymentSetting(ctx context.Context, businessID int64, update grooming.ObSettingUpdate) error {
	ret := _m.Called(ctx, businessID, update)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOBPaymentSetting")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, grooming.ObSettingUpdate) error); ok {
		r0 = rf(ctx, businessID, update)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Client_UpdateOBPaymentSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOBPaymentSetting'
type Client_UpdateOBPaymentSetting_Call struct {
	*mock.Call
}

// UpdateOBPaymentSetting is a helper method to define mock.On call
//   - ctx context.Context
//   - businessID int64
//   - update grooming.ObSettingUpdate
func (_e *Client_Expecter) UpdateOBPaymentSetting(ctx interface{}, businessID interface{}, update interface{}) *Client_UpdateOBPaymentSetting_Call {
	return &Client_UpdateOBPaymentSetting_Call{Call: _e.mock.On("UpdateOBPaymentSetting", ctx, businessID, update)}
}

func (_c *Client_UpdateOBPaymentSetting_Call) Run(run func(ctx context.Context, businessID int64, update grooming.ObSettingUpdate)) *Client_UpdateOBPaymentSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(grooming.ObSettingUpdate))
	})
	return _c
}

func (_c *Client_UpdateOBPaymentSetting_Call) Return(_a0 error) *Client_UpdateOBPaymentSetting_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_UpdateOBPaymentSetting_Call) RunAndReturn(run func(context.Context, int64, grooming.ObSettingUpdate) error) *Client_UpdateOBPaymentSetting_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePetDetailStaff provides a mock function with given fields: ctx, params
func (_m *Client) UpdatePetDetailStaff(ctx context.Context, params []*grooming.EditPetDetailStaffCommissionItem) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePetDetailStaff")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*grooming.EditPetDetailStaffCommissionItem) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Client_UpdatePetDetailStaff_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePetDetailStaff'
type Client_UpdatePetDetailStaff_Call struct {
	*mock.Call
}

// UpdatePetDetailStaff is a helper method to define mock.On call
//   - ctx context.Context
//   - params []*grooming.EditPetDetailStaffCommissionItem
func (_e *Client_Expecter) UpdatePetDetailStaff(ctx interface{}, params interface{}) *Client_UpdatePetDetailStaff_Call {
	return &Client_UpdatePetDetailStaff_Call{Call: _e.mock.On("UpdatePetDetailStaff", ctx, params)}
}

func (_c *Client_UpdatePetDetailStaff_Call) Run(run func(ctx context.Context, params []*grooming.EditPetDetailStaffCommissionItem)) *Client_UpdatePetDetailStaff_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*grooming.EditPetDetailStaffCommissionItem))
	})
	return _c
}

func (_c *Client_UpdatePetDetailStaff_Call) Return(_a0 error) *Client_UpdatePetDetailStaff_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_UpdatePetDetailStaff_Call) RunAndReturn(run func(context.Context, []*grooming.EditPetDetailStaffCommissionItem) error) *Client_UpdatePetDetailStaff_Call {
	_c.Call.Return(run)
	return _c
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
