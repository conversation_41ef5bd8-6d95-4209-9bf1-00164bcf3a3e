// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"
)

// LegacyRefundRepo is an autogenerated mock type for the LegacyRefundRepo type
type LegacyRefundRepo struct {
	mock.Mock
}

type LegacyRefundRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *LegacyRefundRepo) EXPECT() *LegacyRefundRepo_Expecter {
	return &LegacyRefundRepo_Expecter{mock: &_m.Mock}
}

// ListByOrderID provides a mock function with given fields: ctx, orderID
func (_m *LegacyRefundRepo) ListByOrderID(ctx context.Context, orderID int64) ([]*model.RefundOrderPayment, error) {
	ret := _m.Called(ctx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for ListByOrderID")
	}

	var r0 []*model.RefundOrderPayment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.RefundOrderPayment, error)); ok {
		return rf(ctx, orderID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.RefundOrderPayment); ok {
		r0 = rf(ctx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.RefundOrderPayment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LegacyRefundRepo_ListByOrderID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByOrderID'
type LegacyRefundRepo_ListByOrderID_Call struct {
	*mock.Call
}

// ListByOrderID is a helper method to define mock.On call
//   - ctx context.Context
//   - orderID int64
func (_e *LegacyRefundRepo_Expecter) ListByOrderID(ctx interface{}, orderID interface{}) *LegacyRefundRepo_ListByOrderID_Call {
	return &LegacyRefundRepo_ListByOrderID_Call{Call: _e.mock.On("ListByOrderID", ctx, orderID)}
}

func (_c *LegacyRefundRepo_ListByOrderID_Call) Run(run func(ctx context.Context, orderID int64)) *LegacyRefundRepo_ListByOrderID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *LegacyRefundRepo_ListByOrderID_Call) Return(_a0 []*model.RefundOrderPayment, _a1 error) *LegacyRefundRepo_ListByOrderID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LegacyRefundRepo_ListByOrderID_Call) RunAndReturn(run func(context.Context, int64) ([]*model.RefundOrderPayment, error)) *LegacyRefundRepo_ListByOrderID_Call {
	_c.Call.Return(run)
	return _c
}

// NewLegacyRefundRepo creates a new instance of LegacyRefundRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewLegacyRefundRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *LegacyRefundRepo {
	mock := &LegacyRefundRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
