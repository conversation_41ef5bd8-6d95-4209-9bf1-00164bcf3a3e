// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	depositrule "github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/depositrule"
	mock "github.com/stretchr/testify/mock"
)

// CustomerRepo is an autogenerated mock type for the CustomerRepo type
type CustomerRepo struct {
	mock.Mock
}

type CustomerRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *CustomerRepo) EXPECT() *CustomerRepo_Expecter {
	return &CustomerRepo_Expecter{mock: &_m.Mock}
}

// SmartListCountByFilter provides a mock function with given fields: ctx, req
func (_m *CustomerRepo) SmartListCountByFilter(ctx context.Context, req *depositrule.SmartListByFilterRequest) (int, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for SmartListCountByFilter")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *depositrule.SmartListByFilterRequest) (int, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *depositrule.SmartListByFilterRequest) int); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *depositrule.SmartListByFilterRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CustomerRepo_SmartListCountByFilter_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SmartListCountByFilter'
type CustomerRepo_SmartListCountByFilter_Call struct {
	*mock.Call
}

// SmartListCountByFilter is a helper method to define mock.On call
//   - ctx context.Context
//   - req *depositrule.SmartListByFilterRequest
func (_e *CustomerRepo_Expecter) SmartListCountByFilter(ctx interface{}, req interface{}) *CustomerRepo_SmartListCountByFilter_Call {
	return &CustomerRepo_SmartListCountByFilter_Call{Call: _e.mock.On("SmartListCountByFilter", ctx, req)}
}

func (_c *CustomerRepo_SmartListCountByFilter_Call) Run(run func(ctx context.Context, req *depositrule.SmartListByFilterRequest)) *CustomerRepo_SmartListCountByFilter_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*depositrule.SmartListByFilterRequest))
	})
	return _c
}

func (_c *CustomerRepo_SmartListCountByFilter_Call) Return(_a0 int, _a1 error) *CustomerRepo_SmartListCountByFilter_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CustomerRepo_SmartListCountByFilter_Call) RunAndReturn(run func(context.Context, *depositrule.SmartListByFilterRequest) (int, error)) *CustomerRepo_SmartListCountByFilter_Call {
	_c.Call.Return(run)
	return _c
}

// NewCustomerRepo creates a new instance of CustomerRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCustomerRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *CustomerRepo {
	mock := &CustomerRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
