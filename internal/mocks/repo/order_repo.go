// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/MoeGolibrary/moego-svc-order-v2/internal/model"
	mock "github.com/stretchr/testify/mock"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
)

// OrderRepo is an autogenerated mock type for the OrderRepo type
type OrderRepo struct {
	mock.Mock
}

type OrderRepo_Expecter struct {
	mock *mock.Mock
}

func (_m *OrderRepo) EXPECT() *OrderRepo_Expecter {
	return &OrderRepo_Expecter{mock: &_m.Mock}
}

// BatchGetOrders provides a mock function with given fields: ctx, orderIDs
func (_m *OrderRepo) BatchGetOrders(ctx context.Context, orderIDs []int64) ([]*model.Order, error) {
	ret := _m.Called(ctx, orderIDs)

	if len(ret) == 0 {
		panic("no return value specified for BatchGetOrders")
	}

	var r0 []*model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]*model.Order, error)); ok {
		return rf(ctx, orderIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []*model.Order); ok {
		r0 = rf(ctx, orderIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, orderIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderRepo_BatchGetOrders_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchGetOrders'
type OrderRepo_BatchGetOrders_Call struct {
	*mock.Call
}

// BatchGetOrders is a helper method to define mock.On call
//   - ctx context.Context
//   - orderIDs []int64
func (_e *OrderRepo_Expecter) BatchGetOrders(ctx interface{}, orderIDs interface{}) *OrderRepo_BatchGetOrders_Call {
	return &OrderRepo_BatchGetOrders_Call{Call: _e.mock.On("BatchGetOrders", ctx, orderIDs)}
}

func (_c *OrderRepo_BatchGetOrders_Call) Run(run func(ctx context.Context, orderIDs []int64)) *OrderRepo_BatchGetOrders_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *OrderRepo_BatchGetOrders_Call) Return(_a0 []*model.Order, _a1 error) *OrderRepo_BatchGetOrders_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderRepo_BatchGetOrders_Call) RunAndReturn(run func(context.Context, []int64) ([]*model.Order, error)) *OrderRepo_BatchGetOrders_Call {
	_c.Call.Return(run)
	return _c
}

// CancelOrder provides a mock function with given fields: ctx, order
func (_m *OrderRepo) CancelOrder(ctx context.Context, order *model.Order) (int64, error) {
	ret := _m.Called(ctx, order)

	if len(ret) == 0 {
		panic("no return value specified for CancelOrder")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) (int64, error)); ok {
		return rf(ctx, order)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) int64); ok {
		r0 = rf(ctx, order)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Order) error); ok {
		r1 = rf(ctx, order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderRepo_CancelOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelOrder'
type OrderRepo_CancelOrder_Call struct {
	*mock.Call
}

// CancelOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - order *model.Order
func (_e *OrderRepo_Expecter) CancelOrder(ctx interface{}, order interface{}) *OrderRepo_CancelOrder_Call {
	return &OrderRepo_CancelOrder_Call{Call: _e.mock.On("CancelOrder", ctx, order)}
}

func (_c *OrderRepo_CancelOrder_Call) Run(run func(ctx context.Context, order *model.Order)) *OrderRepo_CancelOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Order))
	})
	return _c
}

func (_c *OrderRepo_CancelOrder_Call) Return(_a0 int64, _a1 error) *OrderRepo_CancelOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderRepo_CancelOrder_Call) RunAndReturn(run func(context.Context, *model.Order) (int64, error)) *OrderRepo_CancelOrder_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: ctx, order
func (_m *OrderRepo) Create(ctx context.Context, order *model.Order) error {
	ret := _m.Called(ctx, order)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) error); ok {
		r0 = rf(ctx, order)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OrderRepo_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type OrderRepo_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - order *model.Order
func (_e *OrderRepo_Expecter) Create(ctx interface{}, order interface{}) *OrderRepo_Create_Call {
	return &OrderRepo_Create_Call{Call: _e.mock.On("Create", ctx, order)}
}

func (_c *OrderRepo_Create_Call) Run(run func(ctx context.Context, order *model.Order)) *OrderRepo_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Order))
	})
	return _c
}

func (_c *OrderRepo_Create_Call) Return(_a0 error) *OrderRepo_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderRepo_Create_Call) RunAndReturn(run func(context.Context, *model.Order) error) *OrderRepo_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, id
func (_m *OrderRepo) Get(ctx context.Context, id int64) (*model.Order, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.Order, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.Order); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderRepo_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type OrderRepo_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *OrderRepo_Expecter) Get(ctx interface{}, id interface{}) *OrderRepo_Get_Call {
	return &OrderRepo_Get_Call{Call: _e.mock.On("Get", ctx, id)}
}

func (_c *OrderRepo_Get_Call) Run(run func(ctx context.Context, id int64)) *OrderRepo_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderRepo_Get_Call) Return(_a0 *model.Order, _a1 error) *OrderRepo_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderRepo_Get_Call) RunAndReturn(run func(context.Context, int64) (*model.Order, error)) *OrderRepo_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetForUpdate provides a mock function with given fields: ctx, id
func (_m *OrderRepo) GetForUpdate(ctx context.Context, id int64) (*model.Order, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetForUpdate")
	}

	var r0 *model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*model.Order, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *model.Order); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderRepo_GetForUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetForUpdate'
type OrderRepo_GetForUpdate_Call struct {
	*mock.Call
}

// GetForUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *OrderRepo_Expecter) GetForUpdate(ctx interface{}, id interface{}) *OrderRepo_GetForUpdate_Call {
	return &OrderRepo_GetForUpdate_Call{Call: _e.mock.On("GetForUpdate", ctx, id)}
}

func (_c *OrderRepo_GetForUpdate_Call) Run(run func(ctx context.Context, id int64)) *OrderRepo_GetForUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderRepo_GetForUpdate_Call) Return(_a0 *model.Order, _a1 error) *OrderRepo_GetForUpdate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderRepo_GetForUpdate_Call) RunAndReturn(run func(context.Context, int64) (*model.Order, error)) *OrderRepo_GetForUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// GetRootBySource provides a mock function with given fields: ctx, sourceType, sourceID
func (_m *OrderRepo) GetRootBySource(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64) (*model.Order, error) {
	ret := _m.Called(ctx, sourceType, sourceID)

	if len(ret) == 0 {
		panic("no return value specified for GetRootBySource")
	}

	var r0 *model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) (*model.Order, error)); ok {
		return rf(ctx, sourceType, sourceID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderSourceType, int64) *model.Order); ok {
		r0 = rf(ctx, sourceType, sourceID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, orderpb.OrderSourceType, int64) error); ok {
		r1 = rf(ctx, sourceType, sourceID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderRepo_GetRootBySource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRootBySource'
type OrderRepo_GetRootBySource_Call struct {
	*mock.Call
}

// GetRootBySource is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceType orderpb.OrderSourceType
//   - sourceID int64
func (_e *OrderRepo_Expecter) GetRootBySource(ctx interface{}, sourceType interface{}, sourceID interface{}) *OrderRepo_GetRootBySource_Call {
	return &OrderRepo_GetRootBySource_Call{Call: _e.mock.On("GetRootBySource", ctx, sourceType, sourceID)}
}

func (_c *OrderRepo_GetRootBySource_Call) Run(run func(ctx context.Context, sourceType orderpb.OrderSourceType, sourceID int64)) *OrderRepo_GetRootBySource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(orderpb.OrderSourceType), args[2].(int64))
	})
	return _c
}

func (_c *OrderRepo_GetRootBySource_Call) Return(_a0 *model.Order, _a1 error) *OrderRepo_GetRootBySource_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderRepo_GetRootBySource_Call) RunAndReturn(run func(context.Context, orderpb.OrderSourceType, int64) (*model.Order, error)) *OrderRepo_GetRootBySource_Call {
	_c.Call.Return(run)
	return _c
}

// ListByAppointment provides a mock function with given fields: ctx, appointmentID
func (_m *OrderRepo) ListByAppointment(ctx context.Context, appointmentID int64) ([]*model.Order, error) {
	ret := _m.Called(ctx, appointmentID)

	if len(ret) == 0 {
		panic("no return value specified for ListByAppointment")
	}

	var r0 []*model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.Order, error)); ok {
		return rf(ctx, appointmentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.Order); ok {
		r0 = rf(ctx, appointmentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, appointmentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderRepo_ListByAppointment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByAppointment'
type OrderRepo_ListByAppointment_Call struct {
	*mock.Call
}

// ListByAppointment is a helper method to define mock.On call
//   - ctx context.Context
//   - appointmentID int64
func (_e *OrderRepo_Expecter) ListByAppointment(ctx interface{}, appointmentID interface{}) *OrderRepo_ListByAppointment_Call {
	return &OrderRepo_ListByAppointment_Call{Call: _e.mock.On("ListByAppointment", ctx, appointmentID)}
}

func (_c *OrderRepo_ListByAppointment_Call) Run(run func(ctx context.Context, appointmentID int64)) *OrderRepo_ListByAppointment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderRepo_ListByAppointment_Call) Return(_a0 []*model.Order, _a1 error) *OrderRepo_ListByAppointment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderRepo_ListByAppointment_Call) RunAndReturn(run func(context.Context, int64) ([]*model.Order, error)) *OrderRepo_ListByAppointment_Call {
	_c.Call.Return(run)
	return _c
}

// ListByAppointmentForUpdate provides a mock function with given fields: ctx, appointmentID
func (_m *OrderRepo) ListByAppointmentForUpdate(ctx context.Context, appointmentID int64) ([]*model.Order, error) {
	ret := _m.Called(ctx, appointmentID)

	if len(ret) == 0 {
		panic("no return value specified for ListByAppointmentForUpdate")
	}

	var r0 []*model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.Order, error)); ok {
		return rf(ctx, appointmentID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.Order); ok {
		r0 = rf(ctx, appointmentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, appointmentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderRepo_ListByAppointmentForUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListByAppointmentForUpdate'
type OrderRepo_ListByAppointmentForUpdate_Call struct {
	*mock.Call
}

// ListByAppointmentForUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - appointmentID int64
func (_e *OrderRepo_Expecter) ListByAppointmentForUpdate(ctx interface{}, appointmentID interface{}) *OrderRepo_ListByAppointmentForUpdate_Call {
	return &OrderRepo_ListByAppointmentForUpdate_Call{Call: _e.mock.On("ListByAppointmentForUpdate", ctx, appointmentID)}
}

func (_c *OrderRepo_ListByAppointmentForUpdate_Call) Run(run func(ctx context.Context, appointmentID int64)) *OrderRepo_ListByAppointmentForUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderRepo_ListByAppointmentForUpdate_Call) Return(_a0 []*model.Order, _a1 error) *OrderRepo_ListByAppointmentForUpdate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderRepo_ListByAppointmentForUpdate_Call) RunAndReturn(run func(context.Context, int64) ([]*model.Order, error)) *OrderRepo_ListByAppointmentForUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// ListBySource provides a mock function with given fields: ctx, sourceID, sourceType
func (_m *OrderRepo) ListBySource(ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType) ([]*model.Order, error) {
	ret := _m.Called(ctx, sourceID, sourceType)

	if len(ret) == 0 {
		panic("no return value specified for ListBySource")
	}

	var r0 []*model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, orderpb.OrderSourceType) ([]*model.Order, error)); ok {
		return rf(ctx, sourceID, sourceType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, orderpb.OrderSourceType) []*model.Order); ok {
		r0 = rf(ctx, sourceID, sourceType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, orderpb.OrderSourceType) error); ok {
		r1 = rf(ctx, sourceID, sourceType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderRepo_ListBySource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListBySource'
type OrderRepo_ListBySource_Call struct {
	*mock.Call
}

// ListBySource is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceID int64
//   - sourceType orderpb.OrderSourceType
func (_e *OrderRepo_Expecter) ListBySource(ctx interface{}, sourceID interface{}, sourceType interface{}) *OrderRepo_ListBySource_Call {
	return &OrderRepo_ListBySource_Call{Call: _e.mock.On("ListBySource", ctx, sourceID, sourceType)}
}

func (_c *OrderRepo_ListBySource_Call) Run(run func(ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType)) *OrderRepo_ListBySource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(orderpb.OrderSourceType))
	})
	return _c
}

func (_c *OrderRepo_ListBySource_Call) Return(_a0 []*model.Order, _a1 error) *OrderRepo_ListBySource_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderRepo_ListBySource_Call) RunAndReturn(run func(context.Context, int64, orderpb.OrderSourceType) ([]*model.Order, error)) *OrderRepo_ListBySource_Call {
	_c.Call.Return(run)
	return _c
}

// ListBySourceForUpdate provides a mock function with given fields: ctx, sourceID, sourceType
func (_m *OrderRepo) ListBySourceForUpdate(ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType) ([]*model.Order, error) {
	ret := _m.Called(ctx, sourceID, sourceType)

	if len(ret) == 0 {
		panic("no return value specified for ListBySourceForUpdate")
	}

	var r0 []*model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, orderpb.OrderSourceType) ([]*model.Order, error)); ok {
		return rf(ctx, sourceID, sourceType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, orderpb.OrderSourceType) []*model.Order); ok {
		r0 = rf(ctx, sourceID, sourceType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, orderpb.OrderSourceType) error); ok {
		r1 = rf(ctx, sourceID, sourceType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderRepo_ListBySourceForUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListBySourceForUpdate'
type OrderRepo_ListBySourceForUpdate_Call struct {
	*mock.Call
}

// ListBySourceForUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceID int64
//   - sourceType orderpb.OrderSourceType
func (_e *OrderRepo_Expecter) ListBySourceForUpdate(ctx interface{}, sourceID interface{}, sourceType interface{}) *OrderRepo_ListBySourceForUpdate_Call {
	return &OrderRepo_ListBySourceForUpdate_Call{Call: _e.mock.On("ListBySourceForUpdate", ctx, sourceID, sourceType)}
}

func (_c *OrderRepo_ListBySourceForUpdate_Call) Run(run func(ctx context.Context, sourceID int64, sourceType orderpb.OrderSourceType)) *OrderRepo_ListBySourceForUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(orderpb.OrderSourceType))
	})
	return _c
}

func (_c *OrderRepo_ListBySourceForUpdate_Call) Return(_a0 []*model.Order, _a1 error) *OrderRepo_ListBySourceForUpdate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderRepo_ListBySourceForUpdate_Call) RunAndReturn(run func(context.Context, int64, orderpb.OrderSourceType) ([]*model.Order, error)) *OrderRepo_ListBySourceForUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// ListTailOrder provides a mock function with given fields: ctx, originID
func (_m *OrderRepo) ListTailOrder(ctx context.Context, originID int64) ([]*model.Order, error) {
	ret := _m.Called(ctx, originID)

	if len(ret) == 0 {
		panic("no return value specified for ListTailOrder")
	}

	var r0 []*model.Order
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) ([]*model.Order, error)); ok {
		return rf(ctx, originID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) []*model.Order); ok {
		r0 = rf(ctx, originID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Order)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, originID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderRepo_ListTailOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListTailOrder'
type OrderRepo_ListTailOrder_Call struct {
	*mock.Call
}

// ListTailOrder is a helper method to define mock.On call
//   - ctx context.Context
//   - originID int64
func (_e *OrderRepo_Expecter) ListTailOrder(ctx interface{}, originID interface{}) *OrderRepo_ListTailOrder_Call {
	return &OrderRepo_ListTailOrder_Call{Call: _e.mock.On("ListTailOrder", ctx, originID)}
}

func (_c *OrderRepo_ListTailOrder_Call) Run(run func(ctx context.Context, originID int64)) *OrderRepo_ListTailOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *OrderRepo_ListTailOrder_Call) Return(_a0 []*model.Order, _a1 error) *OrderRepo_ListTailOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderRepo_ListTailOrder_Call) RunAndReturn(run func(context.Context, int64) ([]*model.Order, error)) *OrderRepo_ListTailOrder_Call {
	_c.Call.Return(run)
	return _c
}

// ResetTipAmount provides a mock function with given fields: ctx, order
func (_m *OrderRepo) ResetTipAmount(ctx context.Context, order *model.Order) (int64, error) {
	ret := _m.Called(ctx, order)

	if len(ret) == 0 {
		panic("no return value specified for ResetTipAmount")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) (int64, error)); ok {
		return rf(ctx, order)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Order) int64); ok {
		r0 = rf(ctx, order)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Order) error); ok {
		r1 = rf(ctx, order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderRepo_ResetTipAmount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResetTipAmount'
type OrderRepo_ResetTipAmount_Call struct {
	*mock.Call
}

// ResetTipAmount is a helper method to define mock.On call
//   - ctx context.Context
//   - order *model.Order
func (_e *OrderRepo_Expecter) ResetTipAmount(ctx interface{}, order interface{}) *OrderRepo_ResetTipAmount_Call {
	return &OrderRepo_ResetTipAmount_Call{Call: _e.mock.On("ResetTipAmount", ctx, order)}
}

func (_c *OrderRepo_ResetTipAmount_Call) Run(run func(ctx context.Context, order *model.Order)) *OrderRepo_ResetTipAmount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.Order))
	})
	return _c
}

func (_c *OrderRepo_ResetTipAmount_Call) Return(_a0 int64, _a1 error) *OrderRepo_ResetTipAmount_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderRepo_ResetTipAmount_Call) RunAndReturn(run func(context.Context, *model.Order) (int64, error)) *OrderRepo_ResetTipAmount_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRefund provides a mock function with given fields: ctx, refundOrder
func (_m *OrderRepo) UpdateRefund(ctx context.Context, refundOrder *model.RefundOrder) (int64, error) {
	ret := _m.Called(ctx, refundOrder)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRefund")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrder) (int64, error)); ok {
		return rf(ctx, refundOrder)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.RefundOrder) int64); ok {
		r0 = rf(ctx, refundOrder)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.RefundOrder) error); ok {
		r1 = rf(ctx, refundOrder)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderRepo_UpdateRefund_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRefund'
type OrderRepo_UpdateRefund_Call struct {
	*mock.Call
}

// UpdateRefund is a helper method to define mock.On call
//   - ctx context.Context
//   - refundOrder *model.RefundOrder
func (_e *OrderRepo_Expecter) UpdateRefund(ctx interface{}, refundOrder interface{}) *OrderRepo_UpdateRefund_Call {
	return &OrderRepo_UpdateRefund_Call{Call: _e.mock.On("UpdateRefund", ctx, refundOrder)}
}

func (_c *OrderRepo_UpdateRefund_Call) Run(run func(ctx context.Context, refundOrder *model.RefundOrder)) *OrderRepo_UpdateRefund_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*model.RefundOrder))
	})
	return _c
}

func (_c *OrderRepo_UpdateRefund_Call) Return(_a0 int64, _a1 error) *OrderRepo_UpdateRefund_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OrderRepo_UpdateRefund_Call) RunAndReturn(run func(context.Context, *model.RefundOrder) (int64, error)) *OrderRepo_UpdateRefund_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateSourceForType provides a mock function with given fields: ctx, orderType, oldSourceType, oldSourceID, newSourceType, newSourceID
func (_m *OrderRepo) UpdateSourceForType(ctx context.Context, orderType orderpb.OrderModel_OrderType, oldSourceType orderpb.OrderSourceType, oldSourceID int64, newSourceType orderpb.OrderSourceType, newSourceID int64) error {
	ret := _m.Called(ctx, orderType, oldSourceType, oldSourceID, newSourceType, newSourceID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSourceForType")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, orderpb.OrderModel_OrderType, orderpb.OrderSourceType, int64, orderpb.OrderSourceType, int64) error); ok {
		r0 = rf(ctx, orderType, oldSourceType, oldSourceID, newSourceType, newSourceID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OrderRepo_UpdateSourceForType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateSourceForType'
type OrderRepo_UpdateSourceForType_Call struct {
	*mock.Call
}

// UpdateSourceForType is a helper method to define mock.On call
//   - ctx context.Context
//   - orderType orderpb.OrderModel_OrderType
//   - oldSourceType orderpb.OrderSourceType
//   - oldSourceID int64
//   - newSourceType orderpb.OrderSourceType
//   - newSourceID int64
func (_e *OrderRepo_Expecter) UpdateSourceForType(ctx interface{}, orderType interface{}, oldSourceType interface{}, oldSourceID interface{}, newSourceType interface{}, newSourceID interface{}) *OrderRepo_UpdateSourceForType_Call {
	return &OrderRepo_UpdateSourceForType_Call{Call: _e.mock.On("UpdateSourceForType", ctx, orderType, oldSourceType, oldSourceID, newSourceType, newSourceID)}
}

func (_c *OrderRepo_UpdateSourceForType_Call) Run(run func(ctx context.Context, orderType orderpb.OrderModel_OrderType, oldSourceType orderpb.OrderSourceType, oldSourceID int64, newSourceType orderpb.OrderSourceType, newSourceID int64)) *OrderRepo_UpdateSourceForType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(orderpb.OrderModel_OrderType), args[2].(orderpb.OrderSourceType), args[3].(int64), args[4].(orderpb.OrderSourceType), args[5].(int64))
	})
	return _c
}

func (_c *OrderRepo_UpdateSourceForType_Call) Return(_a0 error) *OrderRepo_UpdateSourceForType_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OrderRepo_UpdateSourceForType_Call) RunAndReturn(run func(context.Context, orderpb.OrderModel_OrderType, orderpb.OrderSourceType, int64, orderpb.OrderSourceType, int64) error) *OrderRepo_UpdateSourceForType_Call {
	_c.Call.Return(run)
	return _c
}

// NewOrderRepo creates a new instance of OrderRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOrderRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *OrderRepo {
	mock := &OrderRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
