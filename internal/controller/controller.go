package controller

import (
	"go.uber.org/zap"

	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/zlog"
	marketingsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	ordersvcv2pb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	temp_ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/temp_order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/config"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/service"
)

type TipsSplitServiceServer struct {
	ordersvcv2pb.UnimplementedSplitTipsServiceServer
	tipsSplitService   service.TipsSplitService
	orderService       service.OrderService
	refundOrderService service.RefundOrderService
}

func NewTipsSplitServiceServer(
	tipsSplitService service.TipsSplitService,
	orderService service.OrderService,
	refundOrderService service.RefundOrderService,
) ordersvcv2pb.SplitTipsServiceServer {
	return &TipsSplitServiceServer{
		tipsSplitService:   tipsSplitService,
		orderService:       orderService,
		refundOrderService: refundOrderService,
	}
}

type OrderServer struct {
	ordersvcpb.UnimplementedOrderServiceServer

	orderService        service.OrderService
	depositOrderService service.DepositOrderService
	refundOrderService  service.RefundOrderService

	consumers map[string]*eventbus.Consumer
}

func NewOrderServer(
	orderService service.OrderService,
	refundOrderService service.RefundOrderService,
	depositOrderService service.DepositOrderService,
	discountCli marketingsvcpb.DiscountCodeServiceClient,
	tipsSplitService service.TipsSplitService,
) ordersvcpb.OrderServiceServer {
	consumers := map[string]*eventbus.Consumer{
		"AppointmentConsumer": newAppointmentConsumer(
			config.EventBus(), orderService, refundOrderService, discountCli,
		),
		"FulfillmentConsumer": newFulfillmentConsumer(
			config.EventBus(), orderService, refundOrderService, discountCli,
		),
		"OrderConsumer":     newOrderConsumer(config.EventBus(), orderService, discountCli),
		"OrderTipsConsumer": NewOrderTipsConsumer(config.EventBus(), tipsSplitService),
	}

	for name, consumer := range consumers {
		if err := consumer.Start(); err != nil {
			zlog.Default().Fatal("consumer start failed", zap.String("consumer", name), zap.Error(err))
		}

		zlog.Default().Info("consumer started", zap.String("consumer", name))
	}

	return &OrderServer{
		orderService:        orderService,
		refundOrderService:  refundOrderService,
		depositOrderService: depositOrderService,
		consumers:           consumers,
	}
}

type AssignItemAmountServer struct {
	temp_ordersvcpb.UnimplementedAssignItemAmountServiceServer
	itemAssignService service.ItemAssignService
}

func NewAssignItemAmountServer(as service.ItemAssignService) temp_ordersvcpb.AssignItemAmountServiceServer {
	return &AssignItemAmountServer{itemAssignService: as}
}

type OrderTaskServer struct {
	ordersvcv2pb.UnimplementedOrderTaskServiceServer
	promotionService service.PromotionService
	orderService     service.OrderService
}

func NewOrderTaskServer(
	promotionService service.PromotionService,
	orderService service.OrderService,
) ordersvcv2pb.OrderTaskServiceServer {
	return &OrderTaskServer{
		promotionService: promotionService,
		orderService:     orderService,
	}
}
