package service

import (
	"context"

	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/util"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type StaffTimeSlotService interface {
	WithQuery(*query.Query) StaffTimeSlotService

	BatchUpdate(context.Context, []dto.UpdateStaffTimeSlotDTO) error
}

type staffTimeSlotService struct {
	query                 *query.Query
	repository            fulfillment.StaffTimeSlotRepository
	fulfillmentRepository fulfillment.Repository
}

func (s staffTimeSlotService) BatchUpdate(ctx context.Context, updates []dto.UpdateStaffTimeSlotDTO) error {
	err := s.query.Transaction(func(tx *query.Query) error {
		if err := s.repository.WithQuery(tx).BatchUpdateSelective(ctx, updates); err != nil {
			return err
		}
		staffTimeSlotIDs := lo.Map(updates, func(u dto.UpdateStaffTimeSlotDTO, _ int) int64 {
			return u.StaffTimeSlotID
		})
		staffTimeSlots, err := s.repository.WithQuery(tx).ListByFilter(ctx, filter.ListStaffTimeSlotFilter{StaffTimeSlotIDs: staffTimeSlotIDs})
		if err != nil {
			return err
		}
		fulfillmentIDs := lo.Map(staffTimeSlots, func(slot *model.StaffTimeSlot, _ int) int64 {
			return slot.FulfillmentID
		})
		allStaffTimeSlots, err := s.repository.WithQuery(tx).ListByFilter(ctx, filter.ListStaffTimeSlotFilter{
			FulfillmentIDs: fulfillmentIDs,
		})
		if err != nil {
			return err
		}
		fulfillmentIDToStaffTimeSlots := lo.GroupBy(allStaffTimeSlots, func(slot *model.StaffTimeSlot) int64 {
			return slot.FulfillmentID
		})
		for fulfillmentID, slots := range fulfillmentIDToStaffTimeSlots {
			startTime, endTime := util.GetTimeRange(slots)
			if err = s.fulfillmentRepository.WithQuery(tx).UpdateSelective(ctx, dto.UpdateFulfillmentDTO{
				FulfillmentID: fulfillmentID,
				StartDatetime: &startTime,
				EndDatetime:   &endTime,
			}); err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (s staffTimeSlotService) WithQuery(q *query.Query) StaffTimeSlotService {
	if q != nil {
		return &staffTimeSlotService{
			query:                 q,
			repository:            s.repository.WithQuery(q),
			fulfillmentRepository: s.fulfillmentRepository.WithQuery(q),
		}
	}
	return s
}

func NewStaffTimeSlotService(
	db *gorm.DB,
	repository fulfillment.StaffTimeSlotRepository,
	fulfillmentRepository fulfillment.Repository,
) StaffTimeSlotService {
	return &staffTimeSlotService{
		query:                 query.Use(db),
		repository:            repository,
		fulfillmentRepository: fulfillmentRepository,
	}
}
